const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class RequestInterceptor {
    constructor(config = {}) {
        this.config = {
            headless: config.headless !== false, // Default to headless
            viewport: config.viewport || { width: 1920, height: 1080 },
            userAgent: config.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            timeout: config.timeout || 30000,
            interceptRules: config.interceptRules || [],
            logRequests: config.logRequests !== false,
            ...config
        };
        this.browser = null;
        this.page = null;
    }

    /**
     * Initialize the browser and page
     */
    async init() {
        try {
            console.log('🚀 Launching browser...');
            this.browser = await puppeteer.launch({
                headless: this.config.headless,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            this.page = await this.browser.newPage();
            
            // Set viewport and user agent
            await this.page.setViewport(this.config.viewport);
            await this.page.setUserAgent(this.config.userAgent);
            
            // Set default timeout
            this.page.setDefaultTimeout(this.config.timeout);

            // Enable request interception
            await this.page.setRequestInterception(true);

            // Set up request interception handler
            this.page.on('request', this.handleRequest.bind(this));

            console.log('✅ Browser initialized successfully');
            return this;
        } catch (error) {
            console.error('❌ Failed to initialize browser:', error);
            throw error;
        }
    }

    /**
     * Handle intercepted requests
     */
    async handleRequest(request) {
        try {
            const url = request.url();
            const method = request.method();
            const postData = request.postData();

            if (this.config.logRequests) {
                console.log(`📡 ${method} ${url}`);
            }

            // Check if this request should be intercepted and modified
            const matchingRule = this.findMatchingRule(url, method);
            
            if (matchingRule && method === 'POST' && postData) {
                console.log(`🎯 Intercepting request: ${url}`);
                await this.modifyAndContinueRequest(request, matchingRule, postData);
            } else {
                // Continue with original request
                await request.continue();
            }
        } catch (error) {
            console.error('❌ Error handling request:', error);
            await request.continue();
        }
    }

    /**
     * Find matching intercept rule for the given URL and method
     */
    findMatchingRule(url, method) {
        return this.config.interceptRules.find(rule => {
            const urlMatches = rule.urlPattern ? new RegExp(rule.urlPattern).test(url) : true;
            const methodMatches = rule.method ? rule.method.toLowerCase() === method.toLowerCase() : true;
            return urlMatches && methodMatches;
        });
    }

    /**
     * Modify request data and continue with modified request
     */
    async modifyAndContinueRequest(request, rule, originalPostData) {
        try {
            let modifiedPostData = originalPostData;

            // Apply regex transformations
            if (rule.transformations && Array.isArray(rule.transformations)) {
                for (const transformation of rule.transformations) {
                    const { pattern, replacement, flags = 'g' } = transformation;
                    const regex = new RegExp(pattern, flags);

                    if (typeof replacement === 'function') {
                        // Handle function-based replacements
                        modifiedPostData = modifiedPostData.replace(regex, replacement);
                    } else {
                        // Handle string-based replacements with dynamic values
                        let finalReplacement = replacement;

                        // Replace placeholders with actual credentials
                        if (finalReplacement.includes('REPLACE_USERNAME') && this.config.credentials.username) {
                            finalReplacement = finalReplacement.replace(/REPLACE_USERNAME/g, this.config.credentials.username);
                        }
                        if (finalReplacement.includes('REPLACE_PASSWORD') && this.config.credentials.password) {
                            finalReplacement = finalReplacement.replace(/REPLACE_PASSWORD/g, this.config.credentials.password);
                        }

                        modifiedPostData = modifiedPostData.replace(regex, finalReplacement);
                    }
                }
            }

            // Log the transformation if enabled
            if (this.config.logRequests) {
                console.log('🔄 POST data transformation:');
                console.log('   Original length:', originalPostData.length);
                console.log('   Modified length:', modifiedPostData.length);
                console.log('   Data changed:', modifiedPostData !== originalPostData ? 'YES' : 'NO');
                if (rule.logData) {
                    console.log('   Original:', originalPostData);
                    console.log('   Modified:', modifiedPostData);
                }
            }

            // Prepare modified headers (excluding content-length as Puppeteer handles it automatically)
            const headers = { ...request.headers() };

            // Remove content-length header as Puppeteer will set it automatically
            delete headers['content-length'];
            delete headers['Content-Length'];

            // Update Content-Type if specified in rule
            if (rule.contentType) {
                headers['content-type'] = rule.contentType;
            }

            // Continue with modified request (Puppeteer will automatically set correct Content-Length)
            await request.continue({
                method: request.method(),
                postData: modifiedPostData,
                headers: headers
            });

            console.log('✅ Request modified and forwarded');
        } catch (error) {
            console.error('❌ Error modifying request:', error);
            await request.continue();
        }
    }

    /**
     * Navigate to a URL
     */
    async goto(url, options = {}) {
        if (!this.page) {
            throw new Error('Browser not initialized. Call init() first.');
        }

        console.log(`🌐 Navigating to: ${url}`);
        return await this.page.goto(url, {
            waitUntil: 'networkidle2',
            ...options
        });
    }

    /**
     * Wait for a specific selector
     */
    async waitForSelector(selector, options = {}) {
        if (!this.page) {
            throw new Error('Browser not initialized. Call init() first.');
        }

        return await this.page.waitForSelector(selector, options);
    }

    /**
     * Execute JavaScript in the page context
     */
    async evaluate(fn, ...args) {
        if (!this.page) {
            throw new Error('Browser not initialized. Call init() first.');
        }

        return await this.page.evaluate(fn, ...args);
    }

    /**
     * Take a screenshot
     */
    async screenshot(options = {}) {
        if (!this.page) {
            throw new Error('Browser not initialized. Call init() first.');
        }

        return await this.page.screenshot({
            fullPage: true,
            ...options
        });
    }

    /**
     * Get the current page instance
     */
    getPage() {
        return this.page;
    }

    /**
     * Close the browser
     */
    async close() {
        if (this.browser) {
            console.log('🔒 Closing browser...');
            await this.browser.close();
            this.browser = null;
            this.page = null;
            console.log('✅ Browser closed');
        }
    }

    /**
     * Add a new intercept rule
     */
    addInterceptRule(rule) {
        this.config.interceptRules.push(rule);
    }

    /**
     * Remove intercept rules by URL pattern
     */
    removeInterceptRule(urlPattern) {
        this.config.interceptRules = this.config.interceptRules.filter(
            rule => rule.urlPattern !== urlPattern
        );
    }

    /**
     * Clear all intercept rules
     */
    clearInterceptRules() {
        this.config.interceptRules = [];
    }
}

module.exports = RequestInterceptor;
