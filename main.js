const { Worker } = require('worker_threads');
const path = require('path');
const axios = require('axios');

// EXTENDED: Extended timeouts for retry completion and session stability
const POOL_SIZE = 1; // Reduced pool size for better resource management
const WORKER_FILE = path.join(__dirname, 'worker.js');
const WORKER_TIMEOUT = 1200000; // EXTENDED: 20 minutes timeout to allow 3 retry attempts + cleanup + delays + buffer
const STATS_INTERVAL = 15000; // CRITICAL FIX: Show stats every 15 seconds for better monitoring

// Configure axios defaults
axios.defaults.timeout = 60000;
axios.defaults.maxRedirects = 5;

// Track active workers and their states
const activeWorkers = new Map();
let totalWorkersCreated = 0;
let totalWorkersTerminated = 0;
let completedTasks = 0;
let totalErrors = 0;

// Worker management functions
function createWorker() {
    const worker = new Worker(WORKER_FILE);
    const workerId = totalWorkersCreated++;
    
    const workerInfo = {
        worker,
        id: workerId,
        startTime: Date.now(),
        tasksCompleted: 0,
        isTerminated: false,
        timeout: null
    };

    // Set worker timeout
    workerInfo.timeout = setTimeout(() => {
        terminateWorker(workerId, 'timeout');
    }, WORKER_TIMEOUT);

    // Handle worker messages
    worker.on('message', (message) => {
        handleWorkerMessage(workerId, message);
    });

    // Handle worker errors
    worker.on('error', (error) => {
        console.error(`Worker ${workerId} error:`, error);
        terminateWorker(workerId, 'error');
    });

    // Handle worker exit
    worker.on('exit', (code) => {
        if (code !== 0) {
            console.error(`Worker ${workerId} exited with code ${code}`);
        }
        cleanupWorker(workerId);
    });

    activeWorkers.set(workerId, workerInfo);
    console.log(`Worker ${workerId} created. Active workers: ${activeWorkers.size}`);
    
    return workerId;
}

function handleWorkerMessage(workerId, message) {
    const workerInfo = activeWorkers.get(workerId);
    if (!workerInfo) return;

    // CRITICAL FIX: Only reset timeout on successful completion, not on every message
    // This prevents timeout reset during retry attempts
    if (!message.error && (message.data || message.status)) {
        if (workerInfo.timeout) {
            clearTimeout(workerInfo.timeout);
            workerInfo.timeout = setTimeout(() => {
                terminateWorker(workerId, 'timeout');
            }, WORKER_TIMEOUT);
        }
    }

    if (message.error) {
        totalErrors++;
        console.error(`Worker ${workerId} error: ${message.error}`);
        
        if (message.completed || message.shouldReplace) {
            terminateWorker(workerId, 'error');
            return;
        }
    }

    if (message.data || message.status) {
        workerInfo.tasksCompleted++;
        completedTasks++;
        
        if (message.status === 200) {
            console.log(`✓ Worker ${workerId} - SUCCESS (200)`);
        } else if (message.status === 403 || message.status === 429) {
            console.log(`⚠ Worker ${workerId} - Akamai block (${message.status})`);
        } else if (message.status === 400) {
            console.log(`✗ Worker ${workerId} - Bad credentials (400)`);
        } else if (message.status === 202) {
            console.log(`✓ Worker ${workerId} - Hit/2FA (202)`);
        } else {
            console.log(`? Worker ${workerId} - Status: ${message.status}`);
        }
    }
}

function terminateWorker(workerId, reason) {
    const workerInfo = activeWorkers.get(workerId);
    if (!workerInfo || workerInfo.isTerminated) return;

    console.log(`Terminating worker ${workerId} (${reason})`);
    
    workerInfo.isTerminated = true;
    
    if (workerInfo.timeout) {
        clearTimeout(workerInfo.timeout);
    }

    try {
        workerInfo.worker.terminate();
    } catch (error) {
        console.error(`Error terminating worker ${workerId}:`, error);
    }

    totalWorkersTerminated++;
}

function cleanupWorker(workerId) {
    activeWorkers.delete(workerId);
    
    // Create new worker to maintain pool size
    if (activeWorkers.size < POOL_SIZE) {
        setTimeout(() => {
            createWorker();
        }, 500); // OPTIMIZED: Reduced delay before creating new worker
    }
}

// Memory optimization
function optimizeMemory() {
    try {
        if (global.gc) {
            global.gc();
        }
    } catch (error) {
        // Ignore gc errors
    }
}

// Display simple stats
function showStats() {
    const uptime = Math.floor(process.uptime());
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    const seconds = uptime % 60;
    
    const memoryUsage = process.memoryUsage();
    const heapUsed = (memoryUsage.heapUsed / 1024 / 1024).toFixed(2);
    const rss = (memoryUsage.rss / 1024 / 1024).toFixed(2);
    
    console.log('\n=== SphereAuto Production Stats ===');
    console.log(`Uptime: ${hours}h ${minutes}m ${seconds}s`);
    console.log(`Active Workers: ${activeWorkers.size}/${POOL_SIZE}`);
    console.log(`Total Tasks: ${completedTasks} | Errors: ${totalErrors}`);
    console.log(`Memory: ${heapUsed}MB heap, ${rss}MB RSS`);
    console.log(`Workers Created: ${totalWorkersCreated} | Terminated: ${totalWorkersTerminated}`);
    console.log('=====================================\n');
}

// Graceful shutdown
let isShuttingDown = false;

function gracefulShutdown() {
    if (isShuttingDown) {
        console.log('Shutdown already in progress...');
        return;
    }

    isShuttingDown = true;
    console.log('\nShutting down gracefully...');

    // Clear all intervals first
    clearInterval(showStatsInterval);
    clearInterval(memoryOptimizationInterval);

    // Terminate all workers
    for (const [workerId] of activeWorkers.entries()) {
        terminateWorker(workerId, 'shutdown');
    }

    // OPTIMIZED: Faster shutdown
    setTimeout(() => {
        console.log('Shutdown complete');
        process.exit(0);
    }, 1500); // Further reduced to 1.5 seconds
}

// Store interval IDs for cleanup
let showStatsInterval;
let memoryOptimizationInterval;

// Main execution
function main() {
    console.log('Starting SphereAuto Production...');
    console.log(`Pool size: ${POOL_SIZE} workers`);
    console.log(`Worker timeout: ${WORKER_TIMEOUT / 1000} seconds`);

    // Create initial worker pool
    for (let i = 0; i < POOL_SIZE; i++) {
        setTimeout(() => {
            createWorker();
        }, i * 500); // Stagger worker creation
    }

    // Show stats periodically
    showStatsInterval = setInterval(showStats, STATS_INTERVAL);

    // Memory optimization every 5 minutes
    memoryOptimizationInterval = setInterval(optimizeMemory, 300000);
    
    // Handle shutdown signals
    process.on('SIGINT', gracefulShutdown);
    process.on('SIGTERM', gracefulShutdown);
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
        console.error('Uncaught Exception:', error);
        gracefulShutdown();
    });
    
    process.on('unhandledRejection', (reason) => {
        console.error('Unhandled Promise Rejection:', reason);
    });
    
    console.log('SphereAuto Production started successfully!\n');
}

// Start the application
if (require.main === module) {
    main();
}

module.exports = { main };
