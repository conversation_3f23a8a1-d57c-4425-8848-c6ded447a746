const config = require('./config');
const { createprofile, stopSession, deleteprofile } = require('./automate');

class SessionManager {
    constructor() {
        this.currentSession = null;
        this.sessionStartTime = null;
        this.sessionTimeout = config.browser.sessionTimeout;
        this.maxSessionAge = config.browser.maxSessionAge;
        this.reuseSession = config.browser.reuseSession;
        this.logSessionEvents = config.logging.enableSessionLogs;
    }

    // Create a new browser session
    async createSession() {
        try {
            if (this.logSessionEvents) {
                console.log('🌐 Creating new browser session...');
            }

            const uuid = await createprofile();
            
            this.currentSession = {
                uuid: uuid,
                createdAt: Date.now(),
                lastUsed: Date.now(),
                isActive: true
            };

            this.sessionStartTime = Date.now();

            if (this.logSessionEvents) {
                console.log(`✅ Browser session created: ${uuid}`);
            }

            return uuid;
        } catch (error) {
            console.error('Failed to create browser session:', error);
            this.currentSession = null;
            this.sessionStartTime = null;
            throw error;
        }
    }

    // Get current session or create new one
    async getSession() {
        // If session reuse is disabled, always create new session
        if (!this.reuseSession) {
            return await this.createSession();
        }

        // Check if current session is valid
        if (this.isSessionValid()) {
            this.currentSession.lastUsed = Date.now();
            if (this.logSessionEvents) {
                console.log(`🔄 Reusing existing session: ${this.currentSession.uuid}`);
            }
            return this.currentSession.uuid;
        }

        // Clean up old session if exists
        if (this.currentSession) {
            await this.cleanupSession();
        }

        // Create new session
        return await this.createSession();
    }

    // Check if current session is valid and not expired
    isSessionValid() {
        if (!this.currentSession || !this.currentSession.isActive) {
            return false;
        }

        const now = Date.now();
        const sessionAge = now - this.currentSession.createdAt;
        const timeSinceLastUse = now - this.currentSession.lastUsed;

        // Check if session is too old
        if (sessionAge > this.maxSessionAge) {
            if (this.logSessionEvents) {
                console.log(`⏰ Session expired due to age: ${sessionAge}ms > ${this.maxSessionAge}ms`);
            }
            return false;
        }

        // Check if session has been idle too long
        if (timeSinceLastUse > this.sessionTimeout) {
            if (this.logSessionEvents) {
                console.log(`⏰ Session expired due to inactivity: ${timeSinceLastUse}ms > ${this.sessionTimeout}ms`);
            }
            return false;
        }

        return true;
    }

    // Clean up current session
    async cleanupSession() {
        if (!this.currentSession) {
            return;
        }

        const uuid = this.currentSession.uuid;
        
        try {
            if (this.logSessionEvents) {
                console.log(`🧹 Cleaning up session: ${uuid}`);
            }

            // Mark session as inactive immediately
            this.currentSession.isActive = false;

            // Stop the session
            if (this.logSessionEvents) {
                console.log(`🛑 Stopping session: ${uuid}`);
            }
            await stopSession(uuid);
            if (this.logSessionEvents) {
                console.log(`✅ Session stopped: ${uuid}`);
            }

            // Delete the profile
            if (this.logSessionEvents) {
                console.log(`🗑️ Deleting profile: ${uuid}`);
            }
            await deleteprofile(uuid);
            if (this.logSessionEvents) {
                console.log(`✅ Profile deleted: ${uuid}`);
            }

            if (this.logSessionEvents) {
                console.log(`✅ Session cleaned up successfully: ${uuid}`);
            }

        } catch (error) {
            console.error(`Error cleaning up session ${uuid}:`, error);
            // Continue anyway, session will be marked as inactive
        } finally {
            this.currentSession = null;
            this.sessionStartTime = null;
        }
    }

    // Force cleanup and create new session
    async refreshSession() {
        if (this.logSessionEvents) {
            console.log('🔄 Forcing session refresh...');
        }

        await this.cleanupSession();
        return await this.createSession();
    }

    // Get session statistics
    getSessionStats() {
        if (!this.currentSession) {
            return {
                hasActiveSession: false,
                sessionAge: 0,
                timeSinceLastUse: 0
            };
        }

        const now = Date.now();
        return {
            hasActiveSession: this.currentSession.isActive,
            uuid: this.currentSession.uuid,
            sessionAge: now - this.currentSession.createdAt,
            timeSinceLastUse: now - this.currentSession.lastUsed,
            isValid: this.isSessionValid()
        };
    }

    // Handle session errors
    async handleSessionError(error) {
        console.error('Session error occurred:', error);

        // Check if it's a critical session error that requires cleanup
        const criticalSessionErrors = [
            'Session is used by another client',
            'Browser process crashed',
            'Page crashed',
            'Session not found',
            'Failed to open a new tab',           // Browser tab creation failure
            'ProtocolError',                      // Puppeteer protocol errors
            'Target.createTarget',                // CDP target creation errors
            'Browser has been closed',            // Browser connection lost
            'Connection closed',                  // WebSocket connection lost
            'Target closed',                      // Browser target closed
            'Session closed',                     // CDP session closed
            'WebSocket is not open',              // WebSocket connection issues
            'Failed to fetch browser webSocket URL',  // Browser connection failure
            'fetch failed',                       // Network connection to browser failed
            'ECONNREFUSED',                       // Connection refused to browser port
            'connect ECONNREFUSED',               // Specific connection refused error
            'getWSEndpoint',                      // WebSocket endpoint fetch failure
            'BrowserConnector'                    // Browser connector errors
        ];

        const errorMessage = error.message || error.toString();
        const errorName = error.name || '';
        const errorStack = error.stack || '';

        // Check for critical error patterns in message, name, or stack
        const isCritical = criticalSessionErrors.some(criticalError =>
            errorMessage.includes(criticalError) ||
            errorName.includes(criticalError) ||
            errorStack.includes(criticalError)
        );

        if (isCritical) {
            if (this.logSessionEvents) {
                console.log('💥 Critical session error detected, forcing cleanup...');
                console.log(`   Error type: ${errorName}`);
                console.log(`   Error message: ${errorMessage}`);
                console.log(`   Matched pattern: ${criticalSessionErrors.find(pattern =>
                    errorMessage.includes(pattern) || errorName.includes(pattern) || errorStack.includes(pattern)
                )}`);
            }
            await this.cleanupSession();
            return true; // Indicates session was cleaned up
        }

        if (this.logSessionEvents) {
            console.log('ℹ️ Non-critical session error, continuing with current session...');
            console.log(`   Error type: ${errorName}`);
            console.log(`   Error message: ${errorMessage}`);
        }

        return false; // Session can continue
    }

    // Graceful shutdown
    async shutdown() {
        if (this.logSessionEvents) {
            console.log('🛑 Session manager shutting down...');
        }

        await this.cleanupSession();
        
        if (this.logSessionEvents) {
            console.log('✅ Session manager shutdown complete');
        }
    }
}

module.exports = SessionManager;
