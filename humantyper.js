// Production-optimized human typing simulation
// Focused on performance and minimal memory usage

class HumanTyper {
    constructor() {
        // Optimized typo mapping - only common keys
        this.typoMap = new Map([
            ['a', ['s', 'q']], ['s', ['a', 'd']], ['d', ['s', 'f']], 
            ['e', ['w', 'r']], ['o', ['i', 'p']], ['l', ['k', 'j']], 
            ['m', ['n']], ['n', ['m']], ['0', ['9']], ['1', ['2']], 
            ['2', ['1', '3']], ['@', ['q', 'w']], ['t', ['r', 'y']], 
            ['r', ['t', 'e']], ['i', ['u', 'o']], ['u', ['i', 'y']], 
            ['p', ['o', 'l']], ['g', ['f', 'h']], ['h', ['g', 'j']], 
            ['k', ['j', 'l']], ['c', ['x', 'v']], ['v', ['c', 'b']], 
            ['b', ['v', 'n']]
        ]);
    }

    // Optimized delay function using page.evaluate for better performance
    async delay(page, ms) {
        return page.evaluate(ms => new Promise(resolve => setTimeout(resolve, ms)), ms);
    }

    // Generate optimized typing delay
    generateDelay(delayRange, isBurst = false) {
        const [min, max] = delayRange;
        let delay = min + Math.random() * (max - min);
        
        // Burst typing optimization
        if (isBurst && Math.random() < 0.15) {
            delay = Math.max(30, delay * 0.4);
        }
        
        return Math.floor(delay);
    }

    // OPTIMIZED typing function for production
    async humanTypeWithTypos(page, selector, text, options = {}) {
        const {
            delayRange = [60, 150],         // OPTIMIZED: Reduced from [80, 180]
            typoChance = 0.02,              // OPTIMIZED: Reduced from 0.03
            pauseChance = 0.02,             // OPTIMIZED: Reduced from 0.03
            burstTyping = true
        } = options;

        try {
            // OPTIMIZED: Reduced timeout for faster processing
            await page.waitForSelector(selector, { visible: true, timeout: 10000 }); // Reduced from 15s to 10s
            await this.delay(page, 150); // Reduced from 200ms to 150ms

            // Focus and clear field efficiently with enhanced error handling
            let focusAttempts = 0;
            while (focusAttempts < 3) {
                try {
                    await page.focus(selector);
                    break;
                } catch (focusError) {
                    console.warn(`Focus attempt ${focusAttempts + 1} failed:`, focusError.message);
                    focusAttempts++;
                    if (focusAttempts < 3) {
                        await this.delay(page, 300); // OPTIMIZED: Reduced from 500ms to 300ms
                    } else {
                        throw focusError;
                    }
                }
            }

            // Enhanced clearing with multiple methods
            await page.keyboard.down('Control');
            await page.keyboard.press('a');
            await page.keyboard.up('Control');
            await this.delay(page, 75); // OPTIMIZED: Reduced from 100ms to 75ms
            await page.keyboard.press('Delete'); // CRITICAL FIX: Actually delete the selected content
            await this.delay(page, 150); // OPTIMIZED: Reduced from 200ms to 150ms

            let typoCount = 0;
            let pauseCount = 0;
            const textLength = text.length;

            // Optimized typing loop
            for (let i = 0; i < textLength; i++) {
                const char = text[i];
                const shouldTypo = Math.random() < typoChance && this.typoMap.has(char);
                const shouldPause = Math.random() < pauseChance;

                // Generate typing delay
                const currentDelay = this.generateDelay(delayRange, burstTyping);

                // Type character (with potential typo)
                if (shouldTypo) {
                    const typoChars = this.typoMap.get(char);
                    const typoChar = typoChars[Math.floor(Math.random() * typoChars.length)];
                    
                    // Type typo
                    await page.keyboard.type(typoChar, { delay: currentDelay });
                    typoCount++;

                    // OPTIMIZED: Faster correction delay
                    await this.delay(page, Math.random() * 300 + 150); // Reduced from [200-600] to [150-450]
                    await page.keyboard.press('Backspace');
                    await this.delay(page, Math.random() * 200 + 75); // Reduced from [100-400] to [75-275]
                    
                    // Type correct character
                    await page.keyboard.type(char, { delay: currentDelay });
                } else {
                    // Type normal character
                    await page.keyboard.type(char, { delay: currentDelay });
                }

                // OPTIMIZED: Handle pauses more efficiently
                if (shouldPause) {
                    pauseCount++;
                    await this.delay(page, Math.random() * 600 + 300); // Reduced from [400-1200] to [300-900]
                }

                // OPTIMIZED: Word boundary pauses
                if (char === ' ' && Math.random() < 0.08) { // Reduced chance from 0.1 to 0.08
                    await this.delay(page, Math.random() * 200 + 150); // Reduced from [200-500] to [150-350]
                }
            }

            // OPTIMIZED: Reduced wait for field update
            await this.delay(page, 200); // Reduced from 300ms to 200ms

            // CRITICAL: Verify the typed text matches what was intended
            const actualValue = await page.evaluate((sel) => {
                const element = document.querySelector(sel);
                return element ? element.value : '';
            }, selector);

            const isCorrect = actualValue === text;
            if (!isCorrect) {
                console.error(`❌ TYPING VERIFICATION FAILED!`);
                console.error(`   Expected: "${text}"`);
                console.error(`   Actual: "${actualValue}"`);
                console.error(`   Selector: ${selector}`);

                // Attempt to fix by retyping with enhanced clearing
                console.log(`🔧 Attempting to fix typing issue...`);

                // Enhanced clearing method
                await page.focus(selector);
                await page.keyboard.down('Control');
                await page.keyboard.press('a');
                await page.keyboard.up('Control');
                await this.delay(page, 100);
                await page.keyboard.press('Delete');
                await this.delay(page, 200);

                // Alternative clearing method if needed
                await page.keyboard.down('Control');
                await page.keyboard.press('a');
                await page.keyboard.up('Control');
                await page.keyboard.press('Backspace');
                await this.delay(page, 100);

                // OPTIMIZED: Simple retype with faster speed
                await page.keyboard.type(text, { delay: 80 }); // Reduced from 100ms to 80ms
                await this.delay(page, 300); // Reduced from 500ms to 300ms

                // Final verification
                const fixedValue = await page.evaluate((sel) => {
                    const element = document.querySelector(sel);
                    return element ? element.value : '';
                }, selector);

                const wasFixed = fixedValue === text;
                console.log(`🔧 Retry result: "${fixedValue}" ${wasFixed ? '✅' : '❌'}`);

                return {
                    success: wasFixed,
                    typos: typoCount,
                    pauses: pauseCount,
                    length: textLength,
                    verified: wasFixed,
                    actualValue: fixedValue,
                    wasRetried: true
                };
            }

            return {
                success: true,
                typos: typoCount,
                pauses: pauseCount,
                length: textLength,
                verified: isCorrect,
                actualValue: actualValue
            };

        } catch (error) {
            console.error('Human typing error:', error);
            return {
                success: false,
                error: error.message,
                typos: 0,
                pauses: 0,
                length: text.length,
                verified: false,
                actualValue: ''
            };
        }
    }

    // OPTIMIZED method for simple typing without typos (faster)
    async simpleType(page, selector, text, options = {}) {
        const { delayRange = [50, 100] } = options; // OPTIMIZED: Reduced from [60, 120]

        try {
            // OPTIMIZED: Ensure element exists with reduced timeout
            await page.waitForSelector(selector, { visible: true, timeout: 8000 }); // Reduced from 10s to 8s
            await this.delay(page, 75); // Reduced from 100ms to 75ms

            await page.focus(selector);
            await page.keyboard.down('Control');
            await page.keyboard.press('a');
            await page.keyboard.up('Control');
            await this.delay(page, 40); // OPTIMIZED: Reduced from 50ms to 40ms
            await page.keyboard.press('Delete'); // CRITICAL FIX: Actually delete the selected content
            await this.delay(page, 75); // OPTIMIZED: Reduced from 100ms to 75ms

            const delay = this.generateDelay(delayRange);
            await page.keyboard.type(text, { delay });
            await this.delay(page, 150); // OPTIMIZED: Reduced from 200ms to 150ms

            // Verify the typed text
            const actualValue = await page.evaluate((sel) => {
                const element = document.querySelector(sel);
                return element ? element.value : '';
            }, selector);

            const isCorrect = actualValue === text;
            if (!isCorrect) {
                console.error(`❌ SIMPLE TYPING VERIFICATION FAILED!`);
                console.error(`   Expected: "${text}"`);
                console.error(`   Actual: "${actualValue}"`);
                console.error(`   Selector: ${selector}`);

                // Attempt one retry
                console.log(`🔧 Attempting simple typing retry...`);
                await page.focus(selector);
                await page.keyboard.down('Control');
                await page.keyboard.press('a');
                await page.keyboard.up('Control');
                await page.keyboard.press('Delete');
                await this.delay(page, 75); // OPTIMIZED: Reduced from 100ms to 75ms
                await page.keyboard.type(text, { delay: 70 }); // OPTIMIZED: Reduced from 80ms to 70ms
                await this.delay(page, 200); // OPTIMIZED: Reduced from 300ms to 200ms

                const retryValue = await page.evaluate((sel) => {
                    const element = document.querySelector(sel);
                    return element ? element.value : '';
                }, selector);

                const retrySuccess = retryValue === text;
                console.log(`🔧 Simple retry result: "${retryValue}" ${retrySuccess ? '✅' : '❌'}`);

                return {
                    success: retrySuccess,
                    length: text.length,
                    verified: retrySuccess,
                    actualValue: retryValue,
                    wasRetried: true
                };
            }

            return {
                success: true,
                length: text.length,
                verified: isCorrect,
                actualValue: actualValue
            };
        } catch (error) {
            console.error('Simple typing error:', error);
            return {
                success: false,
                error: error.message,
                length: text.length,
                verified: false,
                actualValue: ''
            };
        }
    }

    // Clear field method
    async clearField(page, selector) {
        try {
            await page.focus(selector);
            await page.keyboard.down('Control');
            await page.keyboard.press('a');
            await page.keyboard.up('Control');
            await page.keyboard.press('Delete');
            return { success: true };
        } catch (error) {
            console.error('Clear field error:', error);
            return { success: false, error: error.message };
        }
    }
}

// Create singleton instance for better memory management
const humanTyper = new HumanTyper();

// Export the main function for backward compatibility
async function humanTypeWithTypos(page, selector, text, options = {}) {
    return humanTyper.humanTypeWithTypos(page, selector, text, options);
}

module.exports = { 
    humanTypeWithTypos,
    HumanTyper,
    humanTyper
};
