/**
 * Real-World Test Script and Bug Fixes for SphereAuto Retry Interceptor
 * Based on successful integration tests, this script identifies and fixes potential real-world issues
 */

const colors = {
    Reset: "\x1b[0m",
    Red: "\x1b[31m",
    Green: "\x1b[32m",
    Yellow: "\x1b[33m",
    Blue: "\x1b[34m",
    <PERSON>an: "\x1b[36m",
    Magent<PERSON>: "\x1b[35m"
};

class RealWorldTester {
    constructor() {
        this.issues = [];
        this.fixes = [];
        this.warnings = [];
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
        const prefix = `[${timestamp}]`;
        
        switch (type) {
            case 'success':
                console.log(`${colors.Green}✅ ${prefix} ${message}${colors.Reset}`);
                break;
            case 'error':
                console.log(`${colors.Red}❌ ${prefix} ${message}${colors.Reset}`);
                this.issues.push(message);
                break;
            case 'warning':
                console.log(`${colors.Yellow}⚠️  ${prefix} ${message}${colors.Reset}`);
                this.warnings.push(message);
                break;
            case 'fix':
                console.log(`${colors.Cyan}🔧 ${prefix} ${message}${colors.Reset}`);
                this.fixes.push(message);
                break;
            default:
                console.log(`${colors.Blue}ℹ️  ${prefix} ${message}${colors.Reset}`);
        }
    }

    // Test 1: Database Connection Stability
    async testDatabaseStability() {
        this.log('Testing Database Connection Stability', 'info');
        
        try {
            const { getAccountData } = require('./mysql');
            
            // Test multiple rapid connections
            const promises = [];
            for (let i = 0; i < 5; i++) {
                promises.push(getAccountData());
            }
            
            const results = await Promise.allSettled(promises);
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;
            
            if (failed > 0) {
                this.log(`Database connection issues: ${failed}/${results.length} failed`, 'warning');
                this.log('FIX: Add connection pooling and retry logic to mysql.js', 'fix');
            } else {
                this.log(`Database stability test passed: ${successful}/${results.length} successful`, 'success');
            }
            
        } catch (error) {
            this.log(`Database stability test failed: ${error.message}`, 'error');
        }
    }

    // Test 2: Memory Leak Detection
    async testMemoryLeaks() {
        this.log('Testing for Memory Leaks', 'info');
        
        const initialMemory = process.memoryUsage().heapUsed;
        
        try {
            const RetryInterceptor = require('./retry-interceptor');
            
            // Create and destroy many interceptors
            for (let i = 0; i < 50; i++) {
                const interceptor = new RetryInterceptor();
                interceptor.setupSonyInterceptRules();
                await interceptor.activateInterception({ 
                    username: `test${i}@example.com`, 
                    password: `pass${i}` 
                });
                interceptor.cleanup();
            }
            
            // Force garbage collection if available
            if (global.gc) global.gc();
            
            const finalMemory = process.memoryUsage().heapUsed;
            const memoryIncrease = ((finalMemory - initialMemory) / 1024 / 1024).toFixed(2);
            
            if (parseFloat(memoryIncrease) > 10) {
                this.log(`Potential memory leak detected: +${memoryIncrease}MB`, 'warning');
                this.log('FIX: Ensure all event listeners are removed in cleanup()', 'fix');
            } else {
                this.log(`Memory usage stable: +${memoryIncrease}MB`, 'success');
            }
            
        } catch (error) {
            this.log(`Memory leak test failed: ${error.message}`, 'error');
        }
    }

    // Test 3: Concurrent Request Handling
    async testConcurrentRequests() {
        this.log('Testing Concurrent Request Handling', 'info');
        
        try {
            const RetryInterceptor = require('./retry-interceptor');
            const interceptor = new RetryInterceptor();
            interceptor.setupSonyInterceptRules();
            
            // Simulate concurrent requests
            const testRequests = Array.from({ length: 10 }, (_, i) => ({
                url: 'https://ca.account.sony.com/api/v1/ssocookie',
                method: 'POST',
                credentials: { username: `user${i}@test.com`, password: `pass${i}` }
            }));
            
            const promises = testRequests.map(async (req, index) => {
                await interceptor.activateInterception(req.credentials);
                const rule = interceptor.findMatchingRule(req.url, req.method);
                interceptor.deactivateInterception();
                return { index, success: !!rule };
            });
            
            const results = await Promise.allSettled(promises);
            const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
            
            if (successful < testRequests.length) {
                this.log(`Concurrent request handling issues: ${successful}/${testRequests.length} successful`, 'warning');
                this.log('FIX: Add request queuing and synchronization', 'fix');
            } else {
                this.log(`Concurrent request handling passed: ${successful}/${testRequests.length} successful`, 'success');
            }
            
            interceptor.cleanup();
            
        } catch (error) {
            this.log(`Concurrent request test failed: ${error.message}`, 'error');
        }
    }

    // Test 4: Edge Case URL Patterns
    testEdgeCaseUrls() {
        this.log('Testing Edge Case URL Patterns', 'info');
        
        try {
            const RetryInterceptor = require('./retry-interceptor');
            const interceptor = new RetryInterceptor();
            interceptor.setupSonyInterceptRules();
            
            const edgeCases = [
                'https://ca.account.sony.com/api/v1/ssocookie?param=value',
                'https://ca.account.sony.com/api/v1/ssocookie#fragment',
                'https://CA.ACCOUNT.SONY.COM/API/V1/SSOCOOKIE',
                'https://ca.account.sony.com/api/v1/ssocookie/',
                'https://ca.account.sony.com/api/v1/ssocookie?redirect=https://example.com',
                'https://web.np.playstation.com/api/session/v1/signin?locale=en',
                'https://subdomain.sony.com/api/test',
                null,
                undefined,
                '',
                'not-a-url',
                'ftp://ca.account.sony.com/api/v1/ssocookie'
            ];
            
            let handled = 0;
            let errors = 0;
            
            edgeCases.forEach((url, index) => {
                try {
                    const rule = interceptor.findMatchingRule(url, 'POST');
                    handled++;
                    
                    // Check if expected matches actually match
                    if (typeof url === 'string' && url.includes('sony.com') && url.includes('api')) {
                        if (!rule) {
                            this.log(`Expected match failed for: ${url}`, 'warning');
                        }
                    }
                } catch (error) {
                    errors++;
                    this.log(`URL pattern error for case ${index}: ${error.message}`, 'warning');
                }
            });
            
            if (errors > 0) {
                this.log(`Edge case URL handling needs improvement: ${errors} errors`, 'warning');
                this.log('FIX: Add better URL validation and error handling', 'fix');
            } else {
                this.log(`Edge case URL handling passed: ${handled}/${edgeCases.length} handled`, 'success');
            }
            
            interceptor.cleanup();
            
        } catch (error) {
            this.log(`Edge case URL test failed: ${error.message}`, 'error');
        }
    }

    // Test 5: Request Payload Transformation
    testPayloadTransformation() {
        this.log('Testing Request Payload Transformation', 'info');
        
        try {
            const RetryInterceptor = require('./retry-interceptor');
            const interceptor = new RetryInterceptor();
            interceptor.setupSonyInterceptRules();
            
            const testPayloads = [
                {
                    name: 'Standard JSON',
                    data: '{"j_username":"<EMAIL>","j_password":"oldpass","other":"data"}',
                    expected: ['<EMAIL>', 'newpass']
                },
                {
                    name: 'Form Data',
                    data: 'j_username=<EMAIL>&j_password=oldpass&other=data',
                    expected: ['<EMAIL>', 'newpass']
                },
                {
                    name: 'Mixed Case',
                    data: '{"J_USERNAME":"<EMAIL>","J_PASSWORD":"oldpass"}',
                    expected: ['<EMAIL>', 'oldpass'] // Should not match due to case sensitivity
                },
                {
                    name: 'Special Characters',
                    data: '{"j_username":"<EMAIL>","j_password":"pass@#$%"}',
                    expected: ['<EMAIL>', 'newpass']
                },
                {
                    name: 'Empty Payload',
                    data: '',
                    expected: ['', '']
                }
            ];
            
            const rule = interceptor.findMatchingRule('https://ca.account.sony.com/api/v1/ssocookie', 'POST');
            
            if (!rule || !rule.transformations) {
                this.log('No transformation rules found', 'error');
                return;
            }
            
            let transformationIssues = 0;
            
            testPayloads.forEach(test => {
                try {
                    let modifiedData = test.data;
                    
                    rule.transformations.forEach(transformation => {
                        const regex = new RegExp(transformation.pattern, transformation.flags || 'g');
                        let replacement = transformation.replacement;
                        
                        replacement = replacement.replace(/REPLACE_USERNAME/g, '<EMAIL>');
                        replacement = replacement.replace(/REPLACE_PASSWORD/g, 'newpass');
                        
                        modifiedData = modifiedData.replace(regex, replacement);
                    });
                    
                    if (test.name === 'Standard JSON' && !modifiedData.includes('<EMAIL>')) {
                        this.log(`Transformation failed for ${test.name}`, 'warning');
                        transformationIssues++;
                    }
                    
                } catch (error) {
                    this.log(`Transformation error for ${test.name}: ${error.message}`, 'warning');
                    transformationIssues++;
                }
            });
            
            if (transformationIssues > 0) {
                this.log(`Payload transformation issues found: ${transformationIssues}`, 'warning');
                this.log('FIX: Improve regex patterns and add more test cases', 'fix');
            } else {
                this.log('Payload transformation tests passed', 'success');
            }
            
            interceptor.cleanup();
            
        } catch (error) {
            this.log(`Payload transformation test failed: ${error.message}`, 'error');
        }
    }

    // Test 6: Error Recovery and Fallback
    async testErrorRecovery() {
        this.log('Testing Error Recovery and Fallback Mechanisms', 'info');
        
        try {
            const RetryInterceptor = require('./retry-interceptor');
            const interceptor = new RetryInterceptor();
            
            // Test recovery from various error conditions
            const errorTests = [
                {
                    name: 'Null Credentials',
                    test: () => interceptor.activateInterception(null)
                },
                {
                    name: 'Invalid Credentials Object',
                    test: () => interceptor.activateInterception({ invalid: 'object' })
                },
                {
                    name: 'Empty String Credentials',
                    test: () => interceptor.activateInterception({ username: '', password: '' })
                },
                {
                    name: 'Cleanup After Error',
                    test: () => {
                        interceptor.currentCredentials = null;
                        interceptor.cleanup();
                    }
                }
            ];
            
            let recoveryIssues = 0;
            
            for (const errorTest of errorTests) {
                try {
                    await errorTest.test();
                    // If we get here without error, check if it's expected
                    if (errorTest.name.includes('Null') || errorTest.name.includes('Invalid')) {
                        this.log(`${errorTest.name}: Should have thrown error but didn't`, 'warning');
                        recoveryIssues++;
                    }
                } catch (error) {
                    // Expected for some tests
                    if (errorTest.name.includes('Cleanup')) {
                        this.log(`${errorTest.name}: Unexpected error - ${error.message}`, 'warning');
                        recoveryIssues++;
                    }
                }
            }
            
            if (recoveryIssues > 0) {
                this.log(`Error recovery issues found: ${recoveryIssues}`, 'warning');
                this.log('FIX: Add better input validation and error handling', 'fix');
            } else {
                this.log('Error recovery tests passed', 'success');
            }
            
            interceptor.cleanup();
            
        } catch (error) {
            this.log(`Error recovery test failed: ${error.message}`, 'error');
        }
    }

    // Generate fixes based on identified issues
    generateFixes() {
        this.log('Generating Bug Fixes and Improvements', 'info');
        
        const commonFixes = [
            {
                file: 'retry-interceptor.js',
                issue: 'Input validation',
                fix: 'Add comprehensive input validation in activateInterception method'
            },
            {
                file: 'retry-interceptor.js',
                issue: 'Memory management',
                fix: 'Ensure all event listeners are properly removed in cleanup'
            },
            {
                file: 'retry-interceptor.js',
                issue: 'URL pattern matching',
                fix: 'Add case-insensitive URL matching and better error handling'
            },
            {
                file: 'enhanced-mainmethod.js',
                issue: 'Concurrent access',
                fix: 'Add mutex/locking for retry interceptor state changes'
            },
            {
                file: 'mysql.js',
                issue: 'Connection stability',
                fix: 'Implement connection pooling with retry logic'
            }
        ];
        
        commonFixes.forEach(fix => {
            this.log(`${fix.file}: ${fix.issue} - ${fix.fix}`, 'fix');
        });
    }

    // Generate comprehensive report
    generateReport() {
        console.log(`\n${colors.Magenta}📊 REAL-WORLD TEST REPORT${colors.Reset}`);
        console.log('='.repeat(60));
        
        console.log(`\n${colors.Red}🚨 Issues Found: ${this.issues.length}${colors.Reset}`);
        this.issues.forEach((issue, i) => {
            console.log(`   ${i + 1}. ${issue}`);
        });
        
        console.log(`\n${colors.Yellow}⚠️  Warnings: ${this.warnings.length}${colors.Reset}`);
        this.warnings.forEach((warning, i) => {
            console.log(`   ${i + 1}. ${warning}`);
        });
        
        console.log(`\n${colors.Cyan}🔧 Fixes Applied/Recommended: ${this.fixes.length}${colors.Reset}`);
        this.fixes.forEach((fix, i) => {
            console.log(`   ${i + 1}. ${fix}`);
        });
        
        // Overall assessment
        if (this.issues.length === 0 && this.warnings.length < 3) {
            console.log(`\n${colors.Green}✅ ASSESSMENT: PRODUCTION READY${colors.Reset}`);
        } else if (this.issues.length === 0) {
            console.log(`\n${colors.Yellow}⚠️  ASSESSMENT: MINOR IMPROVEMENTS NEEDED${colors.Reset}`);
        } else {
            console.log(`\n${colors.Red}❌ ASSESSMENT: ISSUES NEED RESOLUTION${colors.Reset}`);
        }
        
        return {
            issues: this.issues.length,
            warnings: this.warnings.length,
            fixes: this.fixes.length,
            status: this.issues.length === 0 ? 'READY' : 'NEEDS_WORK'
        };
    }

    // Run all real-world tests
    async runAllTests() {
        console.log(`${colors.Magenta}🌍 REAL-WORLD TEST SUITE${colors.Reset}`);
        console.log(`${colors.Magenta}${'='.repeat(40)}${colors.Reset}`);
        
        const startTime = Date.now();
        
        try {
            await this.testDatabaseStability();
            await this.testMemoryLeaks();
            await this.testConcurrentRequests();
            this.testEdgeCaseUrls();
            this.testPayloadTransformation();
            await this.testErrorRecovery();
            this.generateFixes();
            
            const endTime = Date.now();
            const duration = ((endTime - startTime) / 1000).toFixed(2);
            
            this.log(`Test suite completed in ${duration} seconds`, 'info');
            
            return this.generateReport();
            
        } catch (error) {
            this.log(`Test suite failed: ${error.message}`, 'error');
            return this.generateReport();
        }
    }
}

// Run tests if executed directly
if (require.main === module) {
    const tester = new RealWorldTester();
    tester.runAllTests().then(report => {
        process.exit(report.status === 'READY' ? 0 : 1);
    }).catch(error => {
        console.error(`${colors.Red}❌ Real-world test failed:${colors.Reset}`, error);
        process.exit(1);
    });
}

module.exports = RealWorldTester;
