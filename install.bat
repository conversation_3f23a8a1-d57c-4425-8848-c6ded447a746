@echo off
echo ========================================
echo   SphereAuto Production v2 Installer
echo ========================================
echo.

echo [1/3] Installing Node.js dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [2/3] Checking configuration files...
if not exist "proxy.txt" (
    echo WARNING: proxy.txt not found - please add your proxy servers
)

if not exist "mysql.js" (
    echo ERROR: mysql.js not found
    pause
    exit /b 1
)

echo.
echo [3/3] Installation complete!
echo.
echo ========================================
echo   Ready to run SphereAuto Production v2
echo ========================================
echo.
echo To start the application:
echo   node main.js
echo.
echo To view real-time stats:
echo   The application will show stats every 15 seconds
echo.
echo Configuration:
echo   - Worker Pool Size: 1 worker
echo   - Session Timeout: 8 minutes
echo   - Max Session Age: 15 minutes
echo   - Worker Timeout: 20 minutes
echo.
pause
