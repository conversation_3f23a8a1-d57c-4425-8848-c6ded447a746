// SphereAuto Production Configuration
module.exports = {
    // OPTIMIZED Retry Configuration
    retry: {
        maxAttempts: 3,                    // OPTIMIZED: Reduced from 5 to 3 for faster processing
        retryOnFailures: ['BAD', '2FA', 'GUARD', 'TYPING_ERROR', 'BROWSER_CONNECTION_ERROR'], // Failure types that trigger retry
        delayBetweenRetries: 1500,         // OPTIMIZED: Reduced from 2000ms to 1500ms
        logRetryAttempts: true,            // Enable detailed retry logging
        typingRetryDelay: 500              // OPTIMIZED: Reduced from 1000ms to 500ms
    },

    // OPTIMIZED Browser Session Configuration for Retry Support
    browser: {
        reuseSession: true,                // Reuse browser session across retries
        sessionTimeout: 480000,            // OPTIMIZED: 8 minutes session timeout
        maxSessionAge: 900000              // OPTIMIZED: 15 minutes maximum session age
    },

    // OPTIMIZED Database Configuration
    database: {
        connectionTimeout: 20000,          // OPTIMIZED: Reduced from 30s to 20s
        retryDelay: 500,                   // OPTIMIZED: Reduced from 1000ms to 500ms
        autoUnlockStuck: true,             // Automatically unlock stuck accounts when no accounts found
        maxAutoUnlockAttempts: 1           // Maximum auto-unlock attempts per getAccountData call
    },

    // OPTIMIZED Worker Configuration
    worker: {
        maxConsecutiveErrors: 2,           // OPTIMIZED: Reduced from 3 to 2 for faster recovery
        errorCooldown: 3000                // OPTIMIZED: Reduced from 5000ms to 3000ms
    },

    // Logging Configuration
    logging: {
        enableRetryLogs: true,             // Enable retry-specific logging
        enableSessionLogs: true,           // Enable session management logging
        enableAccountLogs: true            // Enable account processing logging
    }
};
