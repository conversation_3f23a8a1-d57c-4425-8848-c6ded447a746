const axios = require('axios');
const fs = require('fs');

// Production automation module with proper port management
const connections = new Map();
const baseURL = 'http://127.0.0.1:40080';

// OPTIMIZED: Configure axios for production
axios.defaults.timeout = 20000; // OPTIMIZED: Reduced from 30s to 20s
axios.defaults.headers.common['Connection'] = 'keep-alive';
axios.defaults.headers.common['Keep-Alive'] = 'timeout=240, max=800'; // OPTIMIZED: Reduced timeout and max

// Port management with expanded range and better cleanup
const portManager = {
    usedPorts: new Map(),
    basePort: 40080,
    maxPort: 40500, // Expanded range for more workers
    portTimeout: 120000, // OPTIMIZED: 2 minutes (reduced from 3 minutes)

    async findAvailablePort() {
        // Clean up expired ports first
        this.cleanupExpiredPorts();

        // Try to find an available port
        for (let port = this.basePort; port <= this.maxPort; port++) {
            if (!this.usedPorts.has(port)) {
                this.usedPorts.set(port, Date.now());
                console.log(`Allocated port: ${port} (${this.usedPorts.size} ports in use)`);
                return port;
            }
        }

        // If no ports available, force cleanup and try again
        console.log('No ports available, forcing cleanup...');
        this.forceCleanup();

        for (let port = this.basePort; port <= this.maxPort; port++) {
            if (!this.usedPorts.has(port)) {
                this.usedPorts.set(port, Date.now());
                console.log(`Allocated port after cleanup: ${port}`);
                return port;
            }
        }

        throw new Error(`No available ports in range ${this.basePort}-${this.maxPort}. Used: ${this.usedPorts.size}`);
    },

    releasePort(port) {
        if (this.usedPorts.delete(port)) {
            console.log(`Released port: ${port} (${this.usedPorts.size} ports remaining)`);
        }
    },

    cleanupExpiredPorts() {
        const now = Date.now();
        let cleaned = 0;
        for (const [port, timestamp] of this.usedPorts.entries()) {
            if (now - timestamp > this.portTimeout) {
                this.usedPorts.delete(port);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            console.log(`Cleaned up ${cleaned} expired ports`);
        }
    },

    forceCleanup() {
        const before = this.usedPorts.size;
        this.usedPorts.clear();
        console.log(`Force cleaned ${before} ports`);
    },

    getStats() {
        return {
            usedPorts: this.usedPorts.size,
            totalRange: this.maxPort - this.basePort + 1,
            availablePorts: (this.maxPort - this.basePort + 1) - this.usedPorts.size
        };
    }
};

// Connection management
function addConnection(uuid, connectionInfo) {
    connections.set(uuid, {
        ...connectionInfo,
        createdAt: Date.now(),
        lastUsed: Date.now()
    });
}

function getConnection(uuid) {
    return connections.get(uuid);
}

async function createprofile() {
    let port = null;
    let retryCount = 0;
    const MAX_RETRIES = 3; // OPTIMIZED: Reduced from 5 to 3
    const RETRY_DELAY = 5000; // OPTIMIZED: Reduced from 10s to 5s

    try {
        port = await portManager.findAvailablePort();

        while (retryCount <= MAX_RETRIES) {
            try {
                console.log(`Attempting to create profile on port ${port} (attempt ${retryCount + 1}/${MAX_RETRIES + 1})`);

                const response = await axios.post(`http://127.0.0.1:${port}/sessions/create_quick`,
                    { count: 1 },
                    {
                        timeout: 20000, // OPTIMIZED: Reduced from 30s to 20s
                        headers: {
                            'Content-Type': 'application/json',
                            'Connection': 'keep-alive',
                            'Keep-Alive': 'timeout=240, max=800' // OPTIMIZED: Reduced timeout and max
                        }
                    }
                );

                if (!response.data || !response.data[0]?.uuid) {
                    throw new Error('Invalid response format from create_quick');
                }

                const [{ uuid }] = response.data;
                addConnection(uuid, { port });
                console.log(`✓ Profile created successfully: ${uuid} on port ${port}`);
                return uuid;

            } catch (error) {
                if (error.code === 'ECONNREFUSED') {
                    console.log(`⚠ Connection refused on port ${port}, retrying in ${RETRY_DELAY/1000} seconds...`);
                    retryCount++;

                    if (retryCount <= MAX_RETRIES) {
                        // Wait before retry, but keep the same port
                        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                        continue;
                    } else {
                        console.error(`✗ Max retries reached for port ${port}, giving up`);
                        throw new Error(`Failed to connect to Sphere service on port ${port} after ${MAX_RETRIES + 1} attempts`);
                    }
                } else {
                    // For other errors, don't retry
                    console.error(`✗ Non-connection error on port ${port}:`, error.message);
                    throw error;
                }
            }
        }
    } catch (error) {
        // Release the port if we allocated one but failed
        if (port) {
            portManager.releasePort(port);
        }
        console.error('Error creating profile:', error.message);
        throw error;
    }
}

async function stopSession(uuid, retries = 2) {
    const conn = getConnection(uuid);
    if (!conn) throw new Error('No connection found for profile');

    for (let attempt = 1; attempt <= retries + 1; attempt++) {
        try {
            const timeout = attempt === 1 ? 3000 : 5000;

            const response = await axios.post(`http://127.0.0.1:${conn.port}/sessions/stop`, {
                "uuid": uuid
            }, {
                timeout: timeout,
                headers: {
                    'Connection': 'close'
                }
            });

            conn.lastUsed = Date.now();
            return response.data;
        } catch (error) {
            if (attempt === retries + 1) {
                return { uuid: uuid, status: 'assumed_stopped', error: error.message };
            }

            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 3000);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}

async function deleteprofile(uuid, retries = 2) {
    const conn = getConnection(uuid);
    if (!conn) throw new Error('No connection found for profile');

    for (let attempt = 1; attempt <= retries + 1; attempt++) {
        try {
            const timeout = attempt === 1 ? 3000 : 5000;

            const config = {
                method: 'delete',
                maxBodyLength: Infinity,
                url: `http://127.0.0.1:${conn.port}/sessions`,
                headers: {
                    'Content-Type': 'application/json',
                    'Connection': 'close'
                },
                data: JSON.stringify({ uuid: uuid }),
                timeout: timeout,
                validateStatus: function (status) {
                    return status >= 200 && status < 500;
                }
            };

            const response = await axios.request(config);

            // Clean up connection resources
            connections.delete(uuid);
            portManager.releasePort(conn.port);

            return response.data;
        } catch (error) {
            if (attempt === retries + 1) {
                // Final attempt failed - clean up resources anyway
                connections.delete(uuid);
                if (conn) {
                    portManager.releasePort(conn.port);
                }
                return { uuid: uuid, status: 'assumed_deleted', error: error.message };
            }

            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 3000);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}

async function getLine(filePath) {
    try {
        const content = await fs.promises.readFile(filePath, 'utf8');
        const lines = content.split(/\r?\n/).filter(line => line.trim());

        if (lines.length === 0) {
            throw new Error('No valid proxy lines found in file');
        }

        const randomIndex = Math.floor(Math.random() * lines.length);
        const selectedLine = lines[randomIndex];

        if (!selectedLine.includes(':')) {
            throw new Error('Invalid proxy format: missing port separator');
        }

        return selectedLine;
    } catch (error) {
        console.error('Error reading proxy file:', error);
        throw error;
    }
}

async function checkConnectionHealth(uuid) {
    const conn = getConnection(uuid);
    if (!conn) return false;

    try {
        const response = await axios.get(`http://127.0.0.1:${conn.port}/sessions`, {
            timeout: 3000, // OPTIMIZED: Reduced from 5s to 3s
            headers: {
                'Connection': 'keep-alive',
                'Keep-Alive': 'timeout=240, max=800' // OPTIMIZED: Reduced timeout and max
            }
        });
        return response.status === 200;
    } catch (error) {
        return false;
    }
}

async function setconnection(uuid) {
    let retryCount = 0;
    const MAX_RETRIES = 3; // OPTIMIZED: Reduced from 5 to 3
    const RETRY_DELAY = 5000; // OPTIMIZED: Reduced from 10s to 5s for ECONNREFUSED
    let ip, port;

    while (retryCount < MAX_RETRIES) {
        try {
            const line = await getLine('./proxy.txt');
            console.log('Selected proxy:', line);

            [ip, port] = line.split(':').map(part => part.trim());

            if (!ip || !port) {
                throw new Error('Invalid proxy format. Expected format: ip:port');
            }

            const proxyData = {
                "uuid": uuid,
                "type": "http",
                "ip": ip,
                "port": parseInt(port),
                "login": "",
                "password": ""
            };

            let conn = getConnection(uuid);

            if (conn && !(await checkConnectionHealth(uuid))) {
                connections.delete(uuid);
                if (conn.port) {
                    portManager.releasePort(conn.port);
                }
                conn = null;
            }

            if (!conn) {
                const newPort = await portManager.findAvailablePort();
                addConnection(uuid, { port: newPort });
                conn = getConnection(uuid);
            }

            if (!conn) throw new Error('Failed to establish connection');

            console.log(`Setting connection on port ${conn.port} (attempt ${retryCount + 1}/${MAX_RETRIES})`);

            const response = await axios.post(`http://127.0.0.1:${conn.port}/sessions/connection`, proxyData, {
                timeout: 20000, // OPTIMIZED: Reduced from 30s to 20s
                headers: {
                    'Connection': 'keep-alive',
                    'Keep-Alive': 'timeout=240, max=800' // OPTIMIZED: Reduced timeout and max
                },
                validateStatus: function (status) {
                    return status >= 200 && status < 500;
                }
            });

            conn.lastUsed = Date.now();
            console.log(`✓ Connection set successfully on port ${conn.port}`);
            return response.data;
        } catch (error) {
            console.error('Proxy:', ip, ':', parseInt(port), 'Error:', error.code);

            if (error.code === 'ECONNREFUSED') {
                console.log(`⚠ Connection refused on port, retrying in ${RETRY_DELAY/1000} seconds...`);
                retryCount++;

                if (retryCount < MAX_RETRIES) {
                    // Wait before retry, keep the same connection
                    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                    continue;
                } else {
                    throw new Error(`Failed to set connection after ${MAX_RETRIES} attempts: ${error.message}`);
                }
            } else if (error.code === 'ECONNRESET' || error.code === 'ECONNABORTED') {
                retryCount++;
                if (retryCount >= MAX_RETRIES) {
                    throw new Error(`Failed to set connection after ${MAX_RETRIES} attempts: ${error.message}`);
                }

                const conn = getConnection(uuid);
                if (conn) {
                    connections.delete(uuid);
                    if (conn.port) {
                        portManager.releasePort(conn.port);
                    }
                }

                await new Promise(resolve => setTimeout(resolve, 2000 * Math.pow(2, retryCount)));
                continue;
            }

            throw error;
        }
    }
}

async function checkconnection(uuid) {
    let retryCount = 0;
    const MAX_RETRIES = 3;

    while (retryCount < MAX_RETRIES) {
        try {
            let conn = getConnection(uuid);

            if (conn && !(await checkConnectionHealth(uuid))) {
                connections.delete(uuid);
                if (conn.port) {
                    portManager.releasePort(conn.port);
                }
                conn = null;
            }

            if (!conn) {
                return {
                    result: "Failure",
                    error: "No valid connection found for profile"
                };
            }

            const response = await axios.post(`http://127.0.0.1:${conn.port}/sessions/check_proxy`, {
                "uuid": uuid
            }, {
                timeout: 30000,
                headers: {
                    'Connection': 'keep-alive',
                    'Keep-Alive': 'timeout=300, max=1000'
                },
                validateStatus: function (status) {
                    return status >= 200 && status < 500;
                }
            });

            conn.lastUsed = Date.now();
            return response.data;
        } catch (error) {
            console.error('Connection check failed:', error.message);

            if (error.code === 'ECONNRESET' || error.code === 'ECONNABORTED') {
                retryCount++;
                if (retryCount >= MAX_RETRIES) {
                    return {
                        result: "Failure",
                        error: `Failed after ${MAX_RETRIES} attempts: ${error.message}`
                    };
                }

                const conn = getConnection(uuid);
                if (conn) {
                    connections.delete(uuid);
                    if (conn.port) {
                        portManager.releasePort(conn.port);
                    }
                }

                await new Promise(resolve => setTimeout(resolve, 2000 * Math.pow(2, retryCount)));
                continue;
            }

            return {
                result: "Failure",
                error: error.message
            };
        }
    }
}

// Cleanup old connections and ports every 2 minutes (more frequent)
setInterval(() => {
    const now = Date.now();
    const maxAge = 1800000; // 30 minutes (reduced from 1 hour)

    let cleanedConnections = 0;
    for (const [uuid, connection] of connections.entries()) {
        if (now - connection.createdAt > maxAge) {
            if (connection.port) {
                portManager.releasePort(connection.port);
            }
            connections.delete(uuid);
            cleanedConnections++;
        }
    }

    if (cleanedConnections > 0) {
        console.log(`Cleaned up ${cleanedConnections} old connections`);
    }

    // Cleanup expired ports
    portManager.cleanupExpiredPorts();

    // Log port stats every cleanup
    const stats = portManager.getStats();
    console.log(`Port stats: ${stats.usedPorts}/${stats.totalRange} used, ${stats.availablePorts} available`);
}, 120000); // Every 2 minutes

// Additional aggressive cleanup every 30 seconds for ports
setInterval(() => {
    portManager.cleanupExpiredPorts();
}, 30000);

module.exports = {
    createprofile,
    stopSession,
    setconnection,
    checkconnection,
    deleteprofile
};
