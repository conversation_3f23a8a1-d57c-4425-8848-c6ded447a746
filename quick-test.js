/**
 * Quick Test Script - Fast Issue Detection for SphereAuto Retry Interceptor
 * Runs in under 10 seconds to identify critical issues
 */

const colors = {
    Reset: "\x1b[0m",
    Red: "\x1b[31m",
    Green: "\x1b[32m",
    Yellow: "\x1b[33m",
    Blue: "\x1b[34m",
    <PERSON>an: "\x1b[36m"
};

class QuickTest {
    constructor() {
        this.issues = [];
        this.warnings = [];
        this.passed = 0;
        this.failed = 0;
    }

    log(test, passed, message = '', critical = false) {
        if (passed) {
            this.passed++;
            console.log(`${colors.Green}✅ ${test}${colors.Reset}${message ? ` - ${message}` : ''}`);
        } else {
            this.failed++;
            console.log(`${colors.Red}❌ ${test}${colors.Reset}${message ? ` - ${message}` : ''}`);
            if (critical) {
                this.issues.push({ test, message });
            } else {
                this.warnings.push({ test, message });
            }
        }
    }

    // Test 1: Module Loading (Fast)
    testModuleLoading() {
        console.log(`${colors.Cyan}🧪 Module Loading Tests${colors.Reset}`);
        
        try {
            require('./retry-interceptor');
            this.log('RetryInterceptor Module', true);
        } catch (error) {
            this.log('RetryInterceptor Module', false, error.message, true);
        }

        try {
            require('./enhanced-mainmethod');
            this.log('EnhancedMainMethod Module', true);
        } catch (error) {
            this.log('EnhancedMainMethod Module', false, error.message, true);
        }

        try {
            require('./request-interceptor');
            this.log('RequestInterceptor Module', true);
        } catch (error) {
            this.log('RequestInterceptor Module', false, error.message, true);
        }

        try {
            require('./config');
            this.log('Config Module', true);
        } catch (error) {
            this.log('Config Module', false, error.message, true);
        }
    }

    // Test 2: Basic Functionality (Fast)
    testBasicFunctionality() {
        console.log(`\n${colors.Cyan}🧪 Basic Functionality Tests${colors.Reset}`);

        try {
            const RetryInterceptor = require('./retry-interceptor');
            const interceptor = new RetryInterceptor();
            
            // Test initial state
            this.log('RetryInterceptor Creation', true);
            this.log('Initial State Inactive', !interceptor.isActive());
            this.log('No Initial Credentials', !interceptor.getCurrentCredentials());

            // Test rules setup
            interceptor.setupSonyInterceptRules();
            this.log('Sony Rules Setup', true);

            // Test URL matching
            const rule1 = interceptor.findMatchingRule('https://ca.account.sony.com/api/v1/ssocookie', 'POST');
            this.log('Sony URL Matching', !!rule1);

            const rule2 = interceptor.findMatchingRule('https://example.com', 'POST');
            this.log('Non-Sony URL Rejection', !rule2);

            // Test activation
            interceptor.activateInterception({ username: '<EMAIL>', password: 'test123' });
            this.log('Activation Works', interceptor.isActive());

            // Test deactivation
            interceptor.deactivateInterception();
            this.log('Deactivation Works', !interceptor.isActive());

            // Test cleanup
            interceptor.cleanup();
            this.log('Cleanup Works', true);

        } catch (error) {
            this.log('RetryInterceptor Functionality', false, error.message, true);
        }
    }

    // Test 3: Integration Check (Fast)
    testIntegration() {
        console.log(`\n${colors.Cyan}🧪 Integration Tests${colors.Reset}`);

        try {
            const EnhancedMainMethod = require('./enhanced-mainmethod');
            const enhanced = new EnhancedMainMethod();
            
            this.log('EnhancedMainMethod Creation', true);
            this.log('RetryInterceptor Integrated', !!enhanced.retryInterceptor);
            this.log('Initial Interception Mode Off', !enhanced.isInterceptionMode);
            this.log('No Initial Page Reference', !enhanced.currentPage);

            // Test shutdown
            enhanced.shutdown().then(() => {
                this.log('Shutdown Works', true);
            }).catch(error => {
                this.log('Shutdown Works', false, error.message);
            });

        } catch (error) {
            this.log('Enhanced MainMethod Integration', false, error.message, true);
        }
    }

    // Test 4: File Dependencies (Fast)
    testFileDependencies() {
        console.log(`\n${colors.Cyan}🧪 File Dependencies${colors.Reset}`);

        const fs = require('fs');
        
        // Check critical files
        const files = [
            'retry-interceptor.js',
            'enhanced-mainmethod.js',
            'request-interceptor.js',
            'index.js',
            'config.js',
            'mysql.js',
            'proxy.txt'
        ];

        files.forEach(file => {
            const exists = fs.existsSync(file);
            this.log(`File: ${file}`, exists, exists ? 'Found' : 'Missing');
        });
    }

    // Test 5: Configuration Check (Fast)
    testConfiguration() {
        console.log(`\n${colors.Cyan}🧪 Configuration Tests${colors.Reset}`);

        try {
            const config = require('./config');
            
            // Check retry config
            if (config.retry && typeof config.retry.maxAttempts === 'number') {
                this.log('Retry Config', true, `Max attempts: ${config.retry.maxAttempts}`);
            } else {
                this.log('Retry Config', false, 'Missing retry configuration');
            }

            // Check if config has expected structure
            this.log('Config Structure', typeof config === 'object');

        } catch (error) {
            this.log('Configuration Loading', false, error.message, true);
        }

        // Check proxy file content
        try {
            const fs = require('fs');
            if (fs.existsSync('./proxy.txt')) {
                const content = fs.readFileSync('./proxy.txt', 'utf8');
                const lines = content.split('\n').filter(line => line.trim()).length;
                this.log('Proxy File', true, `${lines} proxies found`);
            } else {
                this.log('Proxy File', false, 'proxy.txt not found');
            }
        } catch (error) {
            this.log('Proxy File Check', false, error.message);
        }
    }

    // Test 6: Critical Code Paths (Fast)
    testCriticalCodePaths() {
        console.log(`\n${colors.Cyan}🧪 Critical Code Path Tests${colors.Reset}`);

        try {
            // Test request transformation logic
            const RetryInterceptor = require('./retry-interceptor');
            const interceptor = new RetryInterceptor();
            interceptor.setupSonyInterceptRules();

            // Test JSON transformation
            const testData = '{"j_username":"<EMAIL>","j_password":"oldpass","other":"data"}';
            const rule = interceptor.findMatchingRule('https://ca.account.sony.com/api/v1/ssocookie', 'POST');
            
            if (rule && rule.transformations) {
                this.log('Transformation Rules Exist', true);
                
                // Test pattern matching
                const pattern1 = rule.transformations.find(t => t.pattern.includes('j_username'));
                const pattern2 = rule.transformations.find(t => t.pattern.includes('j_password'));
                
                this.log('Username Pattern', !!pattern1);
                this.log('Password Pattern', !!pattern2);
            } else {
                this.log('Transformation Rules', false, 'No transformation rules found');
            }

            interceptor.cleanup();

        } catch (error) {
            this.log('Critical Code Paths', false, error.message, true);
        }
    }

    // Test 7: Error Handling (Fast)
    testErrorHandling() {
        console.log(`\n${colors.Cyan}🧪 Error Handling Tests${colors.Reset}`);

        try {
            const RetryInterceptor = require('./retry-interceptor');
            const interceptor = new RetryInterceptor();

            // Test null input handling
            try {
                interceptor.activateInterception(null);
                this.log('Null Input Handling', false, 'Should have thrown error');
            } catch (error) {
                this.log('Null Input Handling', true, 'Properly handled');
            }

            // Test invalid URL handling
            const result = interceptor.findMatchingRule(null, 'POST');
            this.log('Invalid URL Handling', !result, 'No match for null URL');

            interceptor.cleanup();

        } catch (error) {
            this.log('Error Handling Tests', false, error.message);
        }
    }

    // Generate quick report
    generateReport() {
        console.log(`\n${colors.Blue}📊 QUICK TEST REPORT${colors.Reset}`);
        console.log('='.repeat(40));
        
        const total = this.passed + this.failed;
        const successRate = total > 0 ? ((this.passed / total) * 100).toFixed(1) : 0;
        
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${colors.Green}${this.passed}${colors.Reset}`);
        console.log(`Failed: ${colors.Red}${this.failed}${colors.Reset}`);
        console.log(`Success Rate: ${successRate}%`);

        if (this.issues.length > 0) {
            console.log(`\n${colors.Red}🚨 CRITICAL ISSUES:${colors.Reset}`);
            this.issues.forEach((issue, i) => {
                console.log(`  ${i + 1}. ${issue.test}: ${issue.message}`);
            });
        }

        if (this.warnings.length > 0) {
            console.log(`\n${colors.Yellow}⚠️  WARNINGS:${colors.Reset}`);
            this.warnings.forEach((warning, i) => {
                console.log(`  ${i + 1}. ${warning.test}: ${warning.message}`);
            });
        }

        // Overall status
        if (this.issues.length === 0 && this.failed === 0) {
            console.log(`\n${colors.Green}✅ STATUS: READY FOR TESTING${colors.Reset}`);
        } else if (this.issues.length > 0) {
            console.log(`\n${colors.Red}❌ STATUS: CRITICAL ISSUES FOUND${colors.Reset}`);
        } else {
            console.log(`\n${colors.Yellow}⚠️  STATUS: MINOR ISSUES FOUND${colors.Reset}`);
        }

        return {
            total,
            passed: this.passed,
            failed: this.failed,
            successRate: parseFloat(successRate),
            issues: this.issues,
            warnings: this.warnings
        };
    }

    // Run all quick tests
    async runQuickTests() {
        const startTime = Date.now();
        
        console.log(`${colors.Cyan}⚡ QUICK TEST SUITE - SphereAuto Retry Interceptor${colors.Reset}`);
        console.log(`${colors.Cyan}${'='.repeat(55)}${colors.Reset}`);

        this.testModuleLoading();
        this.testBasicFunctionality();
        this.testIntegration();
        this.testFileDependencies();
        this.testConfiguration();
        this.testCriticalCodePaths();
        this.testErrorHandling();

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        
        console.log(`\n${colors.Blue}⏱️  Test Duration: ${duration} seconds${colors.Reset}`);
        
        return this.generateReport();
    }
}

// Run quick tests if executed directly
if (require.main === module) {
    const quickTest = new QuickTest();
    quickTest.runQuickTests().then(report => {
        process.exit(report.issues.length > 0 ? 1 : 0);
    }).catch(error => {
        console.error(`${colors.Red}❌ Quick test failed:${colors.Reset}`, error);
        process.exit(1);
    });
}

module.exports = QuickTest;
