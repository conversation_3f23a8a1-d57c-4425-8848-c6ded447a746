const { parentPort } = require('worker_threads');
const { mainmethod, emergencyCleanup } = require('./index');

// Network error types that should trigger retry
const RETRYABLE_ERRORS = [
    'socket hang up',
    'ECONNRESET',
    'ETIMEDOUT',
    'ECONNREFUSED',
    'ENOTFOUND',
    'timeout',
    'network error',
    'Session is used by another client or operation'
];

function isRetryableError(error) {
    const errorMessage = error.message?.toLowerCase() || '';
    return RETRYABLE_ERRORS.some(retryableError => 
        errorMessage.includes(retryableError.toLowerCase())
    );
}

// Optimized memory cleanup
async function clearMemory() {
    try {
        // Clear require cache for non-node_modules files
        Object.keys(require.cache).forEach(key => {
            if (!key.includes('node_modules')) {
                delete require.cache[key];
            }
        });

        // Force garbage collection if available
        if (global.gc) {
            global.gc();
        }

        // Clear any pending timers
        const activeHandles = process._getActiveHandles();
        activeHandles.forEach(handle => {
            if (handle && typeof handle.unref === 'function') {
                handle.unref();
            }
        });

        await new Promise(resolve => setImmediate(resolve));
    } catch (error) {
        console.error('Memory cleanup error:', error);
    }
}

async function workerMain() {
    // CRITICAL FIX: Simplified worker - let Enhanced MainMethod handle all retries
    // Remove worker-level retry logic to avoid conflicts

    while (true) {
        try {
            const result = await mainmethod();

            // Send result to main thread
            parentPort.postMessage({
                data: JSON.stringify(result.data),
                status: result.status,
                error: result.error
            });

            // OPTIMIZED: Reduced delay between tasks
            await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error) {
            console.error(`Worker ${process.pid} error:`, error);

            // CRITICAL FIX: Simple error handling - let Enhanced MainMethod handle retries
            // Just report the error and continue
            parentPort.postMessage({
                error: error.message,
                data: null,
                status: false
            });

            // Clear memory and wait before next attempt
            await clearMemory();
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
}

// Handle worker termination signals with cleanup
process.on('SIGINT', async () => {
    console.log('Worker received SIGINT, performing cleanup before exit...');
    await performEmergencyCleanup();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('Worker received SIGTERM, performing cleanup before exit...');
    await performEmergencyCleanup();
    process.exit(0);
});

// Emergency cleanup function
async function performEmergencyCleanup() {
    try {
        console.log('🚨 EMERGENCY: Performing cleanup before worker termination...');
        await emergencyCleanup();
        console.log('✅ Emergency cleanup completed');
    } catch (error) {
        console.error('❌ Emergency cleanup failed:', error.message);
    }
}

// Handle uncaught exceptions in worker
process.on('uncaughtException', (error) => {
    console.error('Worker uncaught exception:', error);
    parentPort.postMessage({
        error: 'Worker crashed: ' + error.message,
        data: null,
        status: false,
        completed: true
    });
    process.exit(1);
});

workerMain().catch(error => {
    console.error('Worker main error:', error);
    parentPort.postMessage({
        error: 'Worker main error: ' + error.message,
        data: null,
        status: false,
        completed: true
    });
    process.exit(1);
});
