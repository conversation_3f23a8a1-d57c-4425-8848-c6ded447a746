const RequestInterceptor = require('./request-interceptor');
const { getAccountData } = require('./mysql');

/**
 * Enhanced Retry Interceptor for SphereAuto
 * Integrates request-interceptor.js to optimize retry mechanism by intercepting
 * login requests and replacing credentials without manual form filling
 */
class RetryInterceptor {
    constructor() {
        this.interceptor = null;
        this.isInterceptionActive = false;
        this.currentCredentials = null;
        this.interceptRules = [];
        this.colors = {
            Reset: "\x1b[0m",
            Red: "\x1b[31m",
            Green: "\x1b[32m",
            Yellow: "\x1b[33m",
            Blue: "\x1b[34m",
            Cyan: "\x1b[36m"
        };
    }

    /**
     * Initialize the retry interceptor with existing page
     * @param {Page} page - Existing Puppeteer page instance
     * @param {Object} credentials - Initial credentials {username, password}
     */
    async initializeWithPage(page, credentials) {
        try {
            console.log(`${this.colors.Cyan}🔧 Initializing Retry Interceptor...${this.colors.Reset}`);
            
            this.currentCredentials = credentials;
            
            // Setup Sony/PlayStation login intercept rules
            this.setupSonyInterceptRules();
            
            // Enable request interception on existing page
            await page.setRequestInterception(true);
            
            // Set up request handler
            page.on('request', this.handleRequest.bind(this));
            
            console.log(`${this.colors.Green}✅ Retry Interceptor initialized with page${this.colors.Reset}`);
            return true;
        } catch (error) {
            console.error(`${this.colors.Red}❌ Failed to initialize Retry Interceptor:${this.colors.Reset}`, error);
            return false;
        }
    }

    /**
     * Setup intercept rules for Sony/PlayStation authentication endpoints
     */
    setupSonyInterceptRules() {
        this.interceptRules = [
            {
                name: 'Sony SSO Cookie',
                urlPattern: 'https://ca\\.account\\.sony\\.com/api/v1/ssocookie',
                method: 'POST',
                transformations: [
                    {
                        pattern: '"j_username":"[^"]*"',
                        replacement: '"j_username":"REPLACE_USERNAME"',
                        flags: 'g'
                    },
                    {
                        pattern: '"j_password":"[^"]*"',
                        replacement: '"j_password":"REPLACE_PASSWORD"',
                        flags: 'g'
                    }
                ],
                contentType: 'application/json',
                logData: false // Set to true for debugging
            },
            {
                name: 'PlayStation Web API',
                urlPattern: 'https://web\\.np\\.playstation\\.com/api/.*',
                method: 'POST',
                transformations: [
                    {
                        pattern: '"username":"[^"]*"',
                        replacement: '"username":"REPLACE_USERNAME"',
                        flags: 'g'
                    },
                    {
                        pattern: '"password":"[^"]*"',
                        replacement: '"password":"REPLACE_PASSWORD"',
                        flags: 'g'
                    },
                    {
                        pattern: '"j_username":"[^"]*"',
                        replacement: '"j_username":"REPLACE_USERNAME"',
                        flags: 'g'
                    },
                    {
                        pattern: '"j_password":"[^"]*"',
                        replacement: '"j_password":"REPLACE_PASSWORD"',
                        flags: 'g'
                    }
                ],
                contentType: 'application/json',
                logData: false
            },
            {
                name: 'Sony Account API',
                urlPattern: 'https://.*\\.sony\\.com/api/.*',
                method: 'POST',
                transformations: [
                    {
                        pattern: '"j_username":"[^"]*"',
                        replacement: '"j_username":"REPLACE_USERNAME"',
                        flags: 'g'
                    },
                    {
                        pattern: '"j_password":"[^"]*"',
                        replacement: '"j_password":"REPLACE_PASSWORD"',
                        flags: 'g'
                    },
                    {
                        pattern: 'j_username=[^&]*',
                        replacement: 'j_username=REPLACE_USERNAME',
                        flags: 'g'
                    },
                    {
                        pattern: 'j_password=[^&]*',
                        replacement: 'j_password=REPLACE_PASSWORD',
                        flags: 'g'
                    }
                ],
                logData: false
            }
        ];

        console.log(`${this.colors.Blue}📋 Setup ${this.interceptRules.length} intercept rules for Sony/PlayStation${this.colors.Reset}`);
    }

    /**
     * Handle intercepted requests
     */
    async handleRequest(request) {
        try {
            const url = request.url();
            const method = request.method();
            const postData = request.postData();

            // Only intercept if interception is active and it's a POST request with data
            if (!this.isInterceptionActive || method !== 'POST' || !postData) {
                await request.continue();
                return;
            }

            // Check if this request should be intercepted
            const matchingRule = this.findMatchingRule(url, method);
            
            if (matchingRule) {
                console.log(`${this.colors.Cyan}🎯 INTERCEPTING: ${matchingRule.name}${this.colors.Reset}`);
                console.log(`   URL: ${url}`);
                await this.modifyAndContinueRequest(request, matchingRule, postData);
            } else {
                await request.continue();
            }
        } catch (error) {
            console.error(`${this.colors.Red}❌ Error handling request:${this.colors.Reset}`, error);
            await request.continue();
        }
    }

    /**
     * Find matching intercept rule for the given URL and method
     */
    findMatchingRule(url, method) {
        return this.interceptRules.find(rule => {
            const urlMatches = rule.urlPattern ? new RegExp(rule.urlPattern).test(url) : true;
            const methodMatches = rule.method ? rule.method.toLowerCase() === method.toLowerCase() : true;
            return urlMatches && methodMatches;
        });
    }

    /**
     * Modify request data and continue with modified request
     */
    async modifyAndContinueRequest(request, rule, originalPostData) {
        try {
            let modifiedPostData = originalPostData;

            // Apply transformations
            if (rule.transformations && Array.isArray(rule.transformations)) {
                for (const transformation of rule.transformations) {
                    const { pattern, replacement, flags = 'g' } = transformation;
                    const regex = new RegExp(pattern, flags);

                    let finalReplacement = replacement;

                    // Replace placeholders with actual credentials
                    if (finalReplacement.includes('REPLACE_USERNAME') && this.currentCredentials.username) {
                        finalReplacement = finalReplacement.replace(/REPLACE_USERNAME/g, this.currentCredentials.username);
                    }
                    if (finalReplacement.includes('REPLACE_PASSWORD') && this.currentCredentials.password) {
                        finalReplacement = finalReplacement.replace(/REPLACE_PASSWORD/g, this.currentCredentials.password);
                    }

                    modifiedPostData = modifiedPostData.replace(regex, finalReplacement);
                }
            }

            // Log the transformation
            console.log(`${this.colors.Green}🔄 Credentials replaced in request${this.colors.Reset}`);
            console.log(`   Username: ${this.currentCredentials.username}`);
            console.log(`   Data changed: ${modifiedPostData !== originalPostData ? 'YES' : 'NO'}`);
            
            if (rule.logData) {
                console.log('   Original:', originalPostData);
                console.log('   Modified:', modifiedPostData);
            }

            // Prepare headers
            const headers = { ...request.headers() };
            delete headers['content-length'];
            delete headers['Content-Length'];

            if (rule.contentType) {
                headers['content-type'] = rule.contentType;
            }

            // Continue with modified request
            await request.continue({
                method: request.method(),
                postData: modifiedPostData,
                headers: headers
            });

            console.log(`${this.colors.Green}✅ Request intercepted and modified successfully${this.colors.Reset}`);
        } catch (error) {
            console.error(`${this.colors.Red}❌ Error modifying request:${this.colors.Reset}`, error);
            await request.continue();
        }
    }

    /**
     * Activate interception for retry attempts
     * @param {Object} newCredentials - New credentials to use {username, password}
     */
    async activateInterception(newCredentials) {
        console.log(`${this.colors.Yellow}🔄 Activating request interception for retry${this.colors.Reset}`);
        console.log(`   New credentials: ${newCredentials.username}`);
        
        this.currentCredentials = newCredentials;
        this.isInterceptionActive = true;
        
        console.log(`${this.colors.Green}✅ Request interception activated${this.colors.Reset}`);
    }

    /**
     * Deactivate interception
     */
    deactivateInterception() {
        console.log(`${this.colors.Yellow}🛑 Deactivating request interception${this.colors.Reset}`);
        this.isInterceptionActive = false;
    }

    /**
     * Get new account from database and prepare for interception
     */
    async getNewAccountForRetry() {
        try {
            console.log(`${this.colors.Blue}🔍 Fetching new account for retry...${this.colors.Reset}`);
            
            const account = await getAccountData();
            if (!account) {
                console.log(`${this.colors.Yellow}⚠️ No account available for retry${this.colors.Reset}`);
                return null;
            }

            const credentials = {
                username: account.EMAIL,
                password: account.PASSWORD1.replace(/\r\n/g, ''),
                accountId: account.id
            };

            console.log(`${this.colors.Green}✅ New account ready: ${credentials.username}${this.colors.Reset}`);
            return { account, credentials };
        } catch (error) {
            console.error(`${this.colors.Red}❌ Error fetching new account:${this.colors.Reset}`, error);
            return null;
        }
    }

    /**
     * Check if interception is currently active
     */
    isActive() {
        return this.isInterceptionActive;
    }

    /**
     * Get current credentials
     */
    getCurrentCredentials() {
        return this.currentCredentials;
    }

    /**
     * Cleanup interceptor
     */
    cleanup() {
        console.log(`${this.colors.Yellow}🧹 Cleaning up Retry Interceptor...${this.colors.Reset}`);
        this.deactivateInterception();
        this.currentCredentials = null;
        this.interceptRules = [];
        console.log(`${this.colors.Green}✅ Retry Interceptor cleaned up${this.colors.Reset}`);
    }
}

module.exports = RetryInterceptor;
