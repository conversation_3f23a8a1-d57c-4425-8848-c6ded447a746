/**
 * Comprehensive Test Suite for SphereAuto Retry Interceptor Integration
 * Tests all scenarios, edge cases, and real-world use cases
 */

const RetryInterceptor = require('./retry-interceptor');
const EnhancedMainMethod = require('./enhanced-mainmethod');
const { getAccountData, updateCapture, updateBad, updateHit, updateTwo<PERSON>, updateGuard } = require('./mysql');
const config = require('./config');

// Mock Puppeteer page for testing
class MockPage {
    constructor() {
        this.url = () => 'https://ca.account.sony.com/api/v1/signin';
        this.isClosed = () => false;
        this.close = async () => {};
        this.setRequestInterception = async () => {};
        this.on = (event, handler) => {
            this.requestHandler = handler;
        };
        this.goto = async (url) => {
            console.log(`Mock navigation to: ${url}`);
        };
        this.click = async (selector) => {
            console.log(`Mock click on: ${selector}`);
        };
        this.keyboard = {
            press: async (key) => {
                console.log(`Mock key press: ${key}`);
            }
        };
        this.evaluate = async (fn) => {
            return {
                hasEmailField: true,
                hasPasswordField: true,
                hasSubmitButton: true,
                readyState: 'complete'
            };
        };
        this.waitForResponse = async (predicate, options) => {
            return {
                url: () => 'https://ca.account.sony.com/api/v1/ssocookie',
                status: () => 200,
                json: async () => ({ success: true, npsso: 'test_token' })
            };
        };
    }

    // Simulate request interception
    async simulateRequest(url, method, postData) {
        if (this.requestHandler) {
            const mockRequest = {
                url: () => url,
                method: () => method,
                postData: () => postData,
                headers: () => ({ 'content-type': 'application/json' }),
                continue: async (overrides) => {
                    console.log('Mock request continued with overrides:', overrides ? 'YES' : 'NO');
                },
                abort: async () => {
                    console.log('Mock request aborted');
                }
            };
            await this.requestHandler(mockRequest);
        }
    }
}

// Colors for console output
const colors = {
    Reset: "\x1b[0m",
    Red: "\x1b[31m",
    Green: "\x1b[32m",
    Yellow: "\x1b[33m",
    Blue: "\x1b[34m",
    Cyan: "\x1b[36m",
    Magenta: "\x1b[35m"
};

class ComprehensiveTestSuite {
    constructor() {
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.failedTests = 0;
        this.warnings = [];
        this.criticalIssues = [];
    }

    // Test result logging
    logTest(testName, passed, message = '', warning = false, critical = false) {
        this.totalTests++;
        const result = {
            name: testName,
            passed,
            message,
            warning,
            critical,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(result);
        
        if (passed) {
            this.passedTests++;
            console.log(`${colors.Green}✅ ${testName}${colors.Reset}${message ? ` - ${message}` : ''}`);
        } else {
            this.failedTests++;
            console.log(`${colors.Red}❌ ${testName}${colors.Reset}${message ? ` - ${message}` : ''}`);
        }

        if (warning) {
            this.warnings.push(result);
            console.log(`${colors.Yellow}⚠️  WARNING: ${message}${colors.Reset}`);
        }

        if (critical) {
            this.criticalIssues.push(result);
            console.log(`${colors.Red}🚨 CRITICAL: ${message}${colors.Reset}`);
        }
    }

    // Test 1: Basic Component Loading
    async testComponentLoading() {
        console.log(`\n${colors.Cyan}🧪 Test 1: Component Loading${colors.Reset}`);
        console.log('='.repeat(50));

        try {
            const RetryInterceptor = require('./retry-interceptor');
            this.logTest('RetryInterceptor Module Load', true, 'Module loaded successfully');
        } catch (error) {
            this.logTest('RetryInterceptor Module Load', false, error.message, false, true);
        }

        try {
            const EnhancedMainMethod = require('./enhanced-mainmethod');
            this.logTest('EnhancedMainMethod Module Load', true, 'Module loaded successfully');
        } catch (error) {
            this.logTest('EnhancedMainMethod Module Load', false, error.message, false, true);
        }

        try {
            const mysql = require('./mysql');
            this.logTest('MySQL Module Load', true, 'Database module loaded');
        } catch (error) {
            this.logTest('MySQL Module Load', false, error.message, false, true);
        }

        try {
            const config = require('./config');
            this.logTest('Config Module Load', true, 'Configuration loaded');
        } catch (error) {
            this.logTest('Config Module Load', false, error.message, false, true);
        }
    }

    // Test 2: Database Connectivity and Operations
    async testDatabaseOperations() {
        console.log(`\n${colors.Cyan}🧪 Test 2: Database Operations${colors.Reset}`);
        console.log('='.repeat(50));

        try {
            const account = await getAccountData();
            if (account && account.EMAIL && account.PASSWORD1) {
                this.logTest('Database Account Retrieval', true, `Retrieved account: ${account.EMAIL}`);
                
                // Test account data format
                if (account.id && typeof account.id === 'number') {
                    this.logTest('Account ID Format', true, `Valid ID: ${account.id}`);
                } else {
                    this.logTest('Account ID Format', false, 'Invalid or missing account ID', true);
                }

                // Test password format
                if (account.PASSWORD1 && account.PASSWORD1.length > 0) {
                    this.logTest('Password Format', true, 'Password field populated');
                } else {
                    this.logTest('Password Format', false, 'Empty or missing password', false, true);
                }

                // Test email format
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (emailRegex.test(account.EMAIL)) {
                    this.logTest('Email Format Validation', true, 'Valid email format');
                } else {
                    this.logTest('Email Format Validation', false, 'Invalid email format', true);
                }

            } else {
                this.logTest('Database Account Retrieval', false, 'No account data available', false, true);
            }
        } catch (error) {
            this.logTest('Database Connection', false, error.message, false, true);
        }

        // Test database update functions
        try {
            // These are just connection tests, not actual updates
            const updateFunctions = [updateCapture, updateBad, updateHit, updateTwoFA, updateGuard];
            updateFunctions.forEach((func, index) => {
                if (typeof func === 'function') {
                    this.logTest(`Database Update Function ${index + 1}`, true, `${func.name} available`);
                } else {
                    this.logTest(`Database Update Function ${index + 1}`, false, `${func.name} not available`, true);
                }
            });
        } catch (error) {
            this.logTest('Database Update Functions', false, error.message, true);
        }
    }

    // Test 3: RetryInterceptor Functionality
    async testRetryInterceptorFunctionality() {
        console.log(`\n${colors.Cyan}🧪 Test 3: RetryInterceptor Functionality${colors.Reset}`);
        console.log('='.repeat(50));

        const interceptor = new RetryInterceptor();

        // Test initialization
        this.logTest('RetryInterceptor Initialization', true, 'Instance created successfully');

        // Test initial state
        const initialActive = interceptor.isActive();
        this.logTest('Initial Interception State', !initialActive, `Initially inactive: ${!initialActive}`);

        const initialCredentials = interceptor.getCurrentCredentials();
        this.logTest('Initial Credentials State', !initialCredentials, 'No initial credentials set');

        // Test intercept rules setup
        try {
            interceptor.setupSonyInterceptRules();
            this.logTest('Intercept Rules Setup', true, 'Sony/PlayStation rules configured');
        } catch (error) {
            this.logTest('Intercept Rules Setup', false, error.message, false, true);
        }

        // Test URL pattern matching
        const testUrls = [
            { url: 'https://ca.account.sony.com/api/v1/ssocookie', shouldMatch: true },
            { url: 'https://web.np.playstation.com/api/session/v1/signin', shouldMatch: true },
            { url: 'https://example.com/not-matching', shouldMatch: false },
            { url: 'https://google.com', shouldMatch: false }
        ];

        testUrls.forEach(test => {
            const rule = interceptor.findMatchingRule(test.url, 'POST');
            const matches = !!rule;
            this.logTest(
                `URL Pattern Match: ${test.url}`,
                matches === test.shouldMatch,
                `Expected: ${test.shouldMatch}, Got: ${matches}`
            );
        });

        // Test credential activation
        const testCredentials = { username: '<EMAIL>', password: 'testpass123' };
        try {
            await interceptor.activateInterception(testCredentials);
            const isActiveAfter = interceptor.isActive();
            this.logTest('Credential Activation', isActiveAfter, 'Interception activated successfully');

            const currentCreds = interceptor.getCurrentCredentials();
            const credsMatch = currentCreds && currentCreds.username === testCredentials.username;
            this.logTest('Credential Storage', credsMatch, 'Credentials stored correctly');

            // Test deactivation
            interceptor.deactivateInterception();
            const isInactiveAfter = !interceptor.isActive();
            this.logTest('Credential Deactivation', isInactiveAfter, 'Interception deactivated successfully');

        } catch (error) {
            this.logTest('Credential Activation', false, error.message, false, true);
        }

        // Test cleanup
        try {
            interceptor.cleanup();
            this.logTest('Interceptor Cleanup', true, 'Cleanup completed successfully');
        } catch (error) {
            this.logTest('Interceptor Cleanup', false, error.message, true);
        }
    }

    // Test 4: Request Interception Simulation
    async testRequestInterceptionSimulation() {
        console.log(`\n${colors.Cyan}🧪 Test 4: Request Interception Simulation${colors.Reset}`);
        console.log('='.repeat(50));

        const interceptor = new RetryInterceptor();
        const mockPage = new MockPage();

        try {
            // Initialize interceptor with mock page
            const testCredentials = { username: '<EMAIL>', password: 'newpass456' };
            await interceptor.initializeWithPage(mockPage, testCredentials);
            this.logTest('Mock Page Initialization', true, 'Interceptor initialized with mock page');

            // Activate interception
            await interceptor.activateInterception(testCredentials);
            this.logTest('Interception Activation', interceptor.isActive(), 'Interception mode activated');

            // Test request interception with different payloads
            const testRequests = [
                {
                    name: 'Sony SSO JSON Request',
                    url: 'https://ca.account.sony.com/api/v1/ssocookie',
                    method: 'POST',
                    postData: '{"j_username":"<EMAIL>","j_password":"oldpass123","other":"data"}'
                },
                {
                    name: 'PlayStation API Request',
                    url: 'https://web.np.playstation.com/api/session/v1/signin',
                    method: 'POST',
                    postData: '{"username":"<EMAIL>","password":"oldpass123"}'
                },
                {
                    name: 'Form Data Request',
                    url: 'https://ca.account.sony.com/api/v1/ssocookie',
                    method: 'POST',
                    postData: 'j_username=<EMAIL>&j_password=oldpass123&other=data'
                }
            ];

            for (const request of testRequests) {
                try {
                    await mockPage.simulateRequest(request.url, request.method, request.postData);
                    this.logTest(`Request Simulation: ${request.name}`, true, 'Request processed successfully');
                } catch (error) {
                    this.logTest(`Request Simulation: ${request.name}`, false, error.message, true);
                }
            }

        } catch (error) {
            this.logTest('Request Interception Simulation', false, error.message, false, true);
        }

        interceptor.cleanup();
    }

    // Test 5: Enhanced MainMethod Integration
    async testEnhancedMainMethodIntegration() {
        console.log(`\n${colors.Cyan}🧪 Test 5: Enhanced MainMethod Integration${colors.Reset}`);
        console.log('='.repeat(50));

        try {
            const enhancedMain = new EnhancedMainMethod();
            this.logTest('EnhancedMainMethod Instantiation', true, 'Instance created successfully');

            // Test retry interceptor availability
            const hasRetryInterceptor = !!enhancedMain.retryInterceptor;
            this.logTest('RetryInterceptor Integration', hasRetryInterceptor, 'RetryInterceptor integrated');

            // Test initial state
            const initialInterceptionMode = enhancedMain.isInterceptionMode;
            this.logTest('Initial Interception Mode', !initialInterceptionMode, 'Initially not in interception mode');

            const hasCurrentPage = !!enhancedMain.currentPage;
            this.logTest('Initial Page Reference', !hasCurrentPage, 'No initial page reference');

            // Test shutdown procedure
            await enhancedMain.shutdown();
            this.logTest('Enhanced MainMethod Shutdown', true, 'Shutdown completed successfully');

        } catch (error) {
            this.logTest('Enhanced MainMethod Integration', false, error.message, false, true);
        }
    }

    // Test 6: Configuration and Environment
    async testConfigurationAndEnvironment() {
        console.log(`\n${colors.Cyan}🧪 Test 6: Configuration and Environment${colors.Reset}`);
        console.log('='.repeat(50));

        try {
            const config = require('./config');
            
            // Test retry configuration
            if (config.retry && typeof config.retry.maxAttempts === 'number') {
                this.logTest('Retry Configuration', true, `Max attempts: ${config.retry.maxAttempts}`);
            } else {
                this.logTest('Retry Configuration', false, 'Missing or invalid retry configuration', true);
            }

            // Test database configuration
            if (config.database || process.env.DB_HOST) {
                this.logTest('Database Configuration', true, 'Database config available');
            } else {
                this.logTest('Database Configuration', false, 'Missing database configuration', false, true);
            }

        } catch (error) {
            this.logTest('Configuration Loading', false, error.message, false, true);
        }

        // Test proxy file
        try {
            const fs = require('fs');
            const proxyExists = fs.existsSync('./proxy.txt');
            if (proxyExists) {
                const proxyContent = fs.readFileSync('./proxy.txt', 'utf8');
                const proxyCount = proxyContent.split('\n').filter(line => line.trim()).length;
                this.logTest('Proxy Configuration', true, `${proxyCount} proxies available`);
            } else {
                this.logTest('Proxy Configuration', false, 'proxy.txt file not found', true);
            }
        } catch (error) {
            this.logTest('Proxy File Access', false, error.message, true);
        }
    }

    // Test 7: Error Handling and Edge Cases
    async testErrorHandlingAndEdgeCases() {
        console.log(`\n${colors.Cyan}🧪 Test 7: Error Handling and Edge Cases${colors.Reset}`);
        console.log('='.repeat(50));

        const interceptor = new RetryInterceptor();

        // Test null/undefined inputs
        try {
            await interceptor.activateInterception(null);
            this.logTest('Null Credentials Handling', false, 'Should have thrown error', true);
        } catch (error) {
            this.logTest('Null Credentials Handling', true, 'Properly handled null input');
        }

        try {
            await interceptor.activateInterception({ username: '', password: '' });
            this.logTest('Empty Credentials Handling', true, 'Handled empty credentials');
        } catch (error) {
            this.logTest('Empty Credentials Handling', false, error.message, true);
        }

        // Test invalid URL patterns
        const invalidUrls = [
            'not-a-url',
            '',
            null,
            undefined,
            'http://malformed-url'
        ];

        invalidUrls.forEach((url, index) => {
            try {
                const rule = interceptor.findMatchingRule(url, 'POST');
                this.logTest(`Invalid URL Handling ${index + 1}`, !rule, 'No match for invalid URL');
            } catch (error) {
                this.logTest(`Invalid URL Handling ${index + 1}`, true, 'Error properly caught');
            }
        });

        // Test cleanup after errors
        try {
            interceptor.cleanup();
            this.logTest('Cleanup After Errors', true, 'Cleanup successful after error conditions');
        } catch (error) {
            this.logTest('Cleanup After Errors', false, error.message, true);
        }
    }

    // Test 8: Memory and Resource Management
    async testMemoryAndResourceManagement() {
        console.log(`\n${colors.Cyan}🧪 Test 8: Memory and Resource Management${colors.Reset}`);
        console.log('='.repeat(50));

        const initialMemory = process.memoryUsage();
        console.log(`Initial memory usage: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`);

        // Create multiple interceptors to test memory usage
        const interceptors = [];
        for (let i = 0; i < 10; i++) {
            const interceptor = new RetryInterceptor();
            interceptor.setupSonyInterceptRules();
            await interceptor.activateInterception({ username: `test${i}@example.com`, password: `pass${i}` });
            interceptors.push(interceptor);
        }

        const midMemory = process.memoryUsage();
        console.log(`Memory after creating 10 interceptors: ${Math.round(midMemory.heapUsed / 1024 / 1024)}MB`);

        // Cleanup all interceptors
        interceptors.forEach(interceptor => interceptor.cleanup());

        // Force garbage collection if available
        if (global.gc) {
            global.gc();
        }

        const finalMemory = process.memoryUsage();
        console.log(`Memory after cleanup: ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`);

        const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
        const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;

        if (memoryIncreasePercent < 50) {
            this.logTest('Memory Management', true, `Memory increase: ${memoryIncreasePercent.toFixed(2)}%`);
        } else {
            this.logTest('Memory Management', false, `Excessive memory increase: ${memoryIncreasePercent.toFixed(2)}%`, true);
        }
    }

    // Generate comprehensive report
    generateReport() {
        console.log(`\n${colors.Magenta}📊 COMPREHENSIVE TEST REPORT${colors.Reset}`);
        console.log('='.repeat(60));

        console.log(`\n${colors.Blue}📈 Test Statistics:${colors.Reset}`);
        console.log(`   Total Tests: ${this.totalTests}`);
        console.log(`   Passed: ${colors.Green}${this.passedTests}${colors.Reset}`);
        console.log(`   Failed: ${colors.Red}${this.failedTests}${colors.Reset}`);
        console.log(`   Success Rate: ${((this.passedTests / this.totalTests) * 100).toFixed(2)}%`);

        if (this.warnings.length > 0) {
            console.log(`\n${colors.Yellow}⚠️  Warnings (${this.warnings.length}):${colors.Reset}`);
            this.warnings.forEach((warning, index) => {
                console.log(`   ${index + 1}. ${warning.name}: ${warning.message}`);
            });
        }

        if (this.criticalIssues.length > 0) {
            console.log(`\n${colors.Red}🚨 Critical Issues (${this.criticalIssues.length}):${colors.Reset}`);
            this.criticalIssues.forEach((issue, index) => {
                console.log(`   ${index + 1}. ${issue.name}: ${issue.message}`);
            });
        }

        // Overall assessment
        console.log(`\n${colors.Cyan}🎯 Overall Assessment:${colors.Reset}`);
        if (this.criticalIssues.length === 0 && this.failedTests === 0) {
            console.log(`   ${colors.Green}✅ EXCELLENT - All tests passed, ready for production${colors.Reset}`);
        } else if (this.criticalIssues.length === 0 && this.failedTests < 3) {
            console.log(`   ${colors.Yellow}⚠️  GOOD - Minor issues found, review recommended${colors.Reset}`);
        } else if (this.criticalIssues.length > 0) {
            console.log(`   ${colors.Red}❌ CRITICAL ISSUES FOUND - Must be resolved before production${colors.Reset}`);
        }

        return {
            totalTests: this.totalTests,
            passedTests: this.passedTests,
            failedTests: this.failedTests,
            warnings: this.warnings,
            criticalIssues: this.criticalIssues,
            successRate: (this.passedTests / this.totalTests) * 100
        };
    }

    // Run all tests
    async runAllTests() {
        console.log(`${colors.Magenta}🧪 COMPREHENSIVE TEST SUITE - SphereAuto Retry Interceptor${colors.Reset}`);
        console.log(`${colors.Magenta}${'='.repeat(70)}${colors.Reset}`);

        try {
            await this.testComponentLoading();
            await this.testDatabaseOperations();
            await this.testRetryInterceptorFunctionality();
            await this.testRequestInterceptionSimulation();
            await this.testEnhancedMainMethodIntegration();
            await this.testConfigurationAndEnvironment();
            await this.testErrorHandlingAndEdgeCases();
            await this.testMemoryAndResourceManagement();

            return this.generateReport();

        } catch (error) {
            console.error(`${colors.Red}❌ CRITICAL TEST SUITE FAILURE:${colors.Reset}`, error);
            this.logTest('Test Suite Execution', false, error.message, false, true);
            return this.generateReport();
        }
    }
}

// Export for use in other modules
module.exports = ComprehensiveTestSuite;

// Run tests if this file is executed directly
if (require.main === module) {
    const testSuite = new ComprehensiveTestSuite();
    testSuite.runAllTests().then(report => {
        process.exit(report.criticalIssues.length > 0 ? 1 : 0);
    }).catch(error => {
        console.error(`${colors.Red}❌ Test execution failed:${colors.Reset}`, error);
        process.exit(1);
    });
}
