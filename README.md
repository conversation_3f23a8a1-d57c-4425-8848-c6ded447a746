# SphereAuto Production v2

## 🚀 Clean Production Build

This folder contains only the essential files for production deployment of SphereAuto with all optimizations and fixes applied.

## 📁 File Structure

### **Core Application Files**
- `main.js` - Main process manager with worker pool
- `worker.js` - Worker thread implementation
- `index.js` - Core emulation logic with Sony PlayStation automation
- `enhanced-mainmethod.js` - Enhanced main method with retry logic

### **Management Modules**
- `retry-manager.js` - Intelligent retry system for failed attempts
- `session-manager.js` - Browser session lifecycle management
- `mysql.js` - Optimized database operations with connection pooling
- `automate.js` - Browser automation utilities

### **Configuration & Utilities**
- `config.js` - Production configuration with optimized timeouts
- `humantyper.js` - Human-like typing simulation
- `proxy.txt` - Proxy server configuration

### **Dependencies**
- `package.json` - Node.js dependencies
- `package-lock.json` - Locked dependency versions

## ⚙️ Key Features & Optimizations

### **🔄 Retry System**
- **3 retry attempts** per account with different accounts
- **Browser session reuse** across retry attempts
- **Intelligent error classification** (BAD, 2FA, GUARD, TYPING_ERROR)
- **Automatic session cleanup** after all retries

### **🕐 Optimized Timeouts**
- **Browser Session Timeout**: 8 minutes
- **Max Session Age**: 15 minutes  
- **Worker Timeout**: 20 minutes
- **Navigation Timeout**: 60 seconds
- **Element Wait Timeout**: 25 seconds
- **Response Timeout**: 30 seconds

### **🗄️ Database Optimizations**
- **SKIP LOCKED** queries to prevent lock wait timeouts
- **Connection pooling** with 10 concurrent connections
- **Automatic retry logic** for database lock conflicts
- **Emergency account unlocking** for stuck accounts

### **🌐 Browser Management**
- **Session reuse** across retry attempts
- **Connection validation** before reuse
- **Proper cleanup** on worker termination
- **Port management** to prevent resource leaks

## 🚀 Quick Start

### **1. Install Dependencies**
```bash
npm install
```

### **2. Configure Database**
Update MySQL credentials in `mysql.js`:
```javascript
host: 'localhost',
user: 'your_username',
password: 'your_password',
database: 'your_database'
```

### **3. Configure Proxies**
Add proxy servers to `proxy.txt`:
```
proxy1.example.com:8080:username:password
proxy2.example.com:8080:username:password
```

### **4. Run Production**
```bash
node main.js
```

## 📊 Production Stats

The system will display real-time statistics:
- **Active Workers**: Current worker count
- **Total Tasks**: Completed tasks
- **Memory Usage**: Heap and RSS memory
- **Uptime**: System runtime

## 🛠️ Troubleshooting

### **Common Issues**

1. **Lock Wait Timeout**
   - Reduced to 2 workers to prevent database conflicts
   - Added SKIP LOCKED to database queries

2. **Browser Connection Issues**
   - Browser sessions reuse across retries
   - Connection validation before reuse
   - Automatic fallback to new connections

3. **Memory Leaks**
   - Proper session cleanup after retries
   - Worker timeout management
   - Resource monitoring

## 🔧 Configuration

### **Worker Pool Size**
```javascript
const POOL_SIZE = 1; // Adjust based on system capacity
```

### **Retry Configuration**
```javascript
retry: {
    maxAttempts: 3,
    retryOnFailures: ['BAD', '2FA', 'GUARD', 'TYPING_ERROR', 'BROWSER_CONNECTION_ERROR']
}
```

## 📈 Performance

- **Optimized for stability** over speed
- **Resource-efficient** with proper cleanup
- **Database-friendly** with lock prevention
- **Memory-conscious** with session reuse

## 🔒 Security

- **Proxy rotation** for IP anonymization
- **Human-like typing** to avoid detection
- **Session isolation** between attempts
- **Secure credential handling**

---

**Version**: Production v2  
**Last Updated**: June 2025  
**Status**: Production Ready ✅
