/**
 * Test script for Retry Interceptor Integration
 * This script tests the integration of request-interceptor.js with the SphereAuto application
 */

const RetryInterceptor = require('./retry-interceptor');
const { getAccountData } = require('./mysql');

// Colors for console output
const colors = {
    Reset: "\x1b[0m",
    Red: "\x1b[31m",
    Green: "\x1b[32m",
    Yellow: "\x1b[33m",
    Blue: "\x1b[34m",
    Cyan: "\x1b[36m"
};

/**
 * Test the RetryInterceptor class functionality
 */
async function testRetryInterceptor() {
    console.log(`${colors.Cyan}🧪 Testing Retry Interceptor Integration${colors.Reset}`);
    console.log('='.repeat(50));

    const interceptor = new RetryInterceptor();
    
    try {
        // Test 1: Basic initialization
        console.log(`${colors.Blue}Test 1: Basic Initialization${colors.Reset}`);
        console.log(`✅ RetryInterceptor instance created`);
        console.log(`   Interception active: ${interceptor.isActive()}`);
        console.log(`   Current credentials: ${interceptor.getCurrentCredentials() ? 'Set' : 'None'}`);
        
        // Test 2: Database connection
        console.log(`\n${colors.Blue}Test 2: Database Account Retrieval${colors.Reset}`);
        const accountData = await interceptor.getNewAccountForRetry();
        if (accountData) {
            console.log(`✅ Account retrieved successfully`);
            console.log(`   Username: ${accountData.credentials.username}`);
            console.log(`   Account ID: ${accountData.credentials.accountId}`);
        } else {
            console.log(`❌ No account data available`);
        }

        // Test 3: Intercept rules setup
        console.log(`\n${colors.Blue}Test 3: Intercept Rules Configuration${colors.Reset}`);
        interceptor.setupSonyInterceptRules();
        console.log(`✅ Sony/PlayStation intercept rules configured`);
        
        // Test 4: URL pattern matching
        console.log(`\n${colors.Blue}Test 4: URL Pattern Matching${colors.Reset}`);
        const testUrls = [
            'https://ca.account.sony.com/api/v1/ssocookie',
            'https://web.np.playstation.com/api/session/v1/signin',
            'https://example.com/not-matching'
        ];
        
        testUrls.forEach(url => {
            const rule = interceptor.findMatchingRule(url, 'POST');
            console.log(`   ${url}`);
            console.log(`   Match: ${rule ? `✅ ${rule.name}` : '❌ No match'}`);
        });

        // Test 5: Credential activation
        console.log(`\n${colors.Blue}Test 5: Credential Activation${colors.Reset}`);
        if (accountData) {
            await interceptor.activateInterception(accountData.credentials);
            console.log(`✅ Interception activated`);
            console.log(`   Active: ${interceptor.isActive()}`);
            console.log(`   Current username: ${interceptor.getCurrentCredentials().username}`);
            
            // Deactivate
            interceptor.deactivateInterception();
            console.log(`✅ Interception deactivated`);
            console.log(`   Active: ${interceptor.isActive()}`);
        }

        // Test 6: Cleanup
        console.log(`\n${colors.Blue}Test 6: Cleanup${colors.Reset}`);
        interceptor.cleanup();
        console.log(`✅ Cleanup completed`);
        console.log(`   Active: ${interceptor.isActive()}`);
        console.log(`   Credentials: ${interceptor.getCurrentCredentials() ? 'Still set' : 'Cleared'}`);

        console.log(`\n${colors.Green}🎉 All tests completed successfully!${colors.Reset}`);
        
    } catch (error) {
        console.error(`${colors.Red}❌ Test failed:${colors.Reset}`, error);
        console.error(`   Error type: ${error.constructor.name}`);
        console.error(`   Error message: ${error.message}`);
    }
}

/**
 * Test the integration with enhanced-mainmethod.js
 */
async function testEnhancedMainMethodIntegration() {
    console.log(`\n${colors.Cyan}🔗 Testing Enhanced MainMethod Integration${colors.Reset}`);
    console.log('='.repeat(50));

    try {
        // Test loading the enhanced main method
        const EnhancedMainMethod = require('./enhanced-mainmethod');
        console.log(`✅ Enhanced MainMethod loaded successfully`);

        // Test creating instance
        const enhancedMain = new EnhancedMainMethod();
        console.log(`✅ Enhanced MainMethod instance created`);
        console.log(`   Retry interceptor available: ${enhancedMain.retryInterceptor ? 'Yes' : 'No'}`);
        console.log(`   Interception mode: ${enhancedMain.isInterceptionMode ? 'Active' : 'Inactive'}`);

        // Test shutdown
        await enhancedMain.shutdown();
        console.log(`✅ Enhanced MainMethod shutdown completed`);

    } catch (error) {
        console.error(`${colors.Red}❌ Integration test failed:${colors.Reset}`, error);
        console.error(`   Error type: ${error.constructor.name}`);
        console.error(`   Error message: ${error.message}`);
    }
}

/**
 * Test configuration and dependencies
 */
async function testDependencies() {
    console.log(`\n${colors.Cyan}📦 Testing Dependencies${colors.Reset}`);
    console.log('='.repeat(50));

    const dependencies = [
        { name: 'request-interceptor.js', path: './request-interceptor' },
        { name: 'mysql.js', path: './mysql' },
        { name: 'config.js', path: './config' },
        { name: 'enhanced-mainmethod.js', path: './enhanced-mainmethod' }
    ];

    for (const dep of dependencies) {
        try {
            require(dep.path);
            console.log(`✅ ${dep.name} - Available`);
        } catch (error) {
            console.log(`❌ ${dep.name} - Error: ${error.message}`);
        }
    }
}

/**
 * Display integration summary
 */
function displayIntegrationSummary() {
    console.log(`\n${colors.Cyan}📋 Integration Summary${colors.Reset}`);
    console.log('='.repeat(50));
    
    console.log(`${colors.Green}✅ COMPLETED INTEGRATIONS:${colors.Reset}`);
    console.log(`   • RetryInterceptor class created with Sony/PlayStation support`);
    console.log(`   • Enhanced MainMethod modified to include retry interceptor`);
    console.log(`   • Request interception rules configured for authentication endpoints`);
    console.log(`   • Database integration for account retrieval during retries`);
    console.log(`   • Page reference passing from emulation function`);
    console.log(`   • Cleanup and shutdown procedures updated`);
    
    console.log(`\n${colors.Yellow}⚠️ REQUIREMENTS FOR FULL FUNCTIONALITY:${colors.Reset}`);
    console.log(`   • Sphere service must be running on port 40080`);
    console.log(`   • Database must contain account data`);
    console.log(`   • Proxy configuration must be available`);
    console.log(`   • Browser automation service must be accessible`);
    
    console.log(`\n${colors.Blue}🚀 EXPECTED BEHAVIOR:${colors.Reset}`);
    console.log(`   1. First login attempt: Normal human emulation with form filling`);
    console.log(`   2. Subsequent retries: Request interception with credential replacement`);
    console.log(`   3. Reduced processing time and thread blocking on retries`);
    console.log(`   4. Maintained browser session continuity`);
    console.log(`   5. Proper cleanup and error handling`);
    
    console.log(`\n${colors.Cyan}🔧 NEXT STEPS:${colors.Reset}`);
    console.log(`   1. Start the Sphere service on port 40080`);
    console.log(`   2. Ensure database contains test accounts`);
    console.log(`   3. Run the main application: node main.js`);
    console.log(`   4. Monitor logs for retry interception behavior`);
}

/**
 * Main test function
 */
async function runTests() {
    console.log(`${colors.Cyan}🧪 SphereAuto Retry Interceptor Integration Tests${colors.Reset}`);
    console.log(`${colors.Cyan}${'='.repeat(60)}${colors.Reset}`);
    
    try {
        await testDependencies();
        await testRetryInterceptor();
        await testEnhancedMainMethodIntegration();
        displayIntegrationSummary();
        
        console.log(`\n${colors.Green}🎉 ALL INTEGRATION TESTS COMPLETED SUCCESSFULLY!${colors.Reset}`);
        
    } catch (error) {
        console.error(`${colors.Red}❌ CRITICAL TEST FAILURE:${colors.Reset}`, error);
        process.exit(1);
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(error => {
        console.error(`${colors.Red}❌ Test execution failed:${colors.Reset}`, error);
        process.exit(1);
    });
}

module.exports = {
    testRetryInterceptor,
    testEnhancedMainMethodIntegration,
    testDependencies,
    runTests
};
