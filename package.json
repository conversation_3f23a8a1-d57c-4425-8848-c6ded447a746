{"name": "sphereauto-production", "version": "2.0.0", "description": "Production-optimized SphereAuto - Automated Task Processing System", "main": "main.js", "scripts": {"start": "node main.js", "monitor": "node monitor.js", "test": "node index.js"}, "dependencies": {"axios": "^1.8.4", "mysql2": "^3.14.0", "puppeteer": "^24.6.1"}, "engines": {"node": ">=18.0.0"}, "keywords": ["automation", "puppeteer", "worker-threads", "performance", "production"], "author": "SphereAuto Team", "license": "MIT"}